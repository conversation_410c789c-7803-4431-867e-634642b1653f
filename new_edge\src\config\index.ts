import fs from 'fs';
import path from 'path';
import { ServerConfig } from '../types';

// 默认配置
const defaultConfig: ServerConfig = {
  http: {
    listen: '0.0.0.0:8080',
    public: 'http://localhost:8080',
    cors: true
  },
  webrtc: {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' }
    ]
  },
  log: {
    level: 'INFO'
  },
  cascade: {
    mode: 'auto'
  },
  auth: {
    enabled: false
  }
};

/**
 * 加载配置文件
 * @param configPath 配置文件路径
 * @returns 合并后的配置
 */
export function loadConfig(configPath?: string): ServerConfig {
  if (!configPath) {
    return defaultConfig;
  }

  try {
    const configFile = path.resolve(process.cwd(), configPath);
    if (!fs.existsSync(configFile)) {
      console.warn(`配置文件 ${configFile} 不存在，使用默认配置`);
      return defaultConfig;
    }

    const fileContent = fs.readFileSync(configFile, 'utf-8');
    const fileConfig = JSON.parse(fileContent);
    
    // 深度合并配置
    return deepMerge(defaultConfig, fileConfig);
  } catch (error) {
    console.error('加载配置文件失败:', error);
    return defaultConfig;
  }
}

/**
 * 深度合并对象
 */
function deepMerge<T>(target: T, source: any): T {
  const output = { ...target };
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
}

function isObject(item: any): boolean {
  return (item && typeof item === 'object' && !Array.isArray(item));
}