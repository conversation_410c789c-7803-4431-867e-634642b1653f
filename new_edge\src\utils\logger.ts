import { createLogger, format, transports, Logger } from 'winston';
import { ServerConfig } from '../types';

let logger: Logger;

/**
 * 初始化日志系统
 * @param config 服务器配置
 */
export function initLogger(config: ServerConfig): Logger {
  const logLevel = config.log.level.toLowerCase();
  
  const logFormat = format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.printf(({ level, message, timestamp }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${message}`;
    })
  );

  const logTransports = [
    new transports.Console({
      format: format.combine(
        format.colorize(),
        logFormat
      )
    })
  ];

  // 如果配置了日志文件，添加文件传输
  if (config.log.file) {
    logTransports.push(
      new transports.File({
        filename: config.log.file,
        format: logFormat
      })
    );
  }

  logger = createLogger({
    level: logLevel,
    transports: logTransports
  });

  return logger;
}

/**
 * 获取日志实例
 */
export function getLogger(): Logger {
  if (!logger) {
    throw new Error('日志系统未初始化');
  }
  return logger;
}