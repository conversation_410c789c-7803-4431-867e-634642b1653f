import { getLogger } from '../utils/logger';

const logger = getLogger();

// 简单的事件发射器实现
class SimpleEventEmitter {
  private listeners = new Map<string, Function[]>();

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }
}

/**
 * RTP包信息
 */
export interface RtpPacket {
  data: Uint8Array;
  timestamp: number;
  sequenceNumber: number;
  ssrc: number;
  payloadType: number;
  isKeyFrame: boolean;
}

/**
 * RTCP包信息
 */
export interface RtcpPacket {
  data: Uint8Array;
  type: RtcpType;
  ssrc: number;
}

/**
 * RTCP包类型
 */
export enum RtcpType {
  SR = 200,    // 发送者报告
  RR = 201,    // 接收者报告
  SDES = 202,  // 源描述
  BYE = 203,   // 再见
  APP = 204,   // 应用程序定义
  RTPFB = 205, // RTP反馈
  PSFB = 206   // 有效负载特定反馈
}

/**
 * RTP转发器 - 负责RTP包的转发和RTCP处理
 */
export class RtpForwarder extends SimpleEventEmitter {
  private publishers = new Map<string, Map<string, RTCRtpSender>>();
  private subscribers = new Map<string, Map<string, RTCRtpReceiver>>();
  private rtpBuffers = new Map<string, RtpPacket[]>();
  private keyFrameRequested = new Set<string>();
  
  // 缓冲区大小 (毫秒)
  private bufferSize = 500;
  
  constructor() {
    super();
    
    // 定期清理过期的RTP包
    setInterval(() => this.cleanupRtpBuffers(), 1000);
  }
  
  /**
   * 注册发布者轨道
   * @param streamId 流ID
   * @param trackId 轨道ID
   * @param sender RTP发送器
   */
  public registerPublisherTrack(streamId: string, trackId: string, sender: RTCRtpSender): void {
    if (!this.publishers.has(streamId)) {
      this.publishers.set(streamId, new Map());
    }
    
    this.publishers.get(streamId)!.set(trackId, sender);
    logger.info(`注册发布者轨道: ${streamId}/${trackId}`);
    
    // 初始化RTP缓冲区
    const bufferKey = `${streamId}/${trackId}`;
    this.rtpBuffers.set(bufferKey, []);
  }
  
  /**
   * 注册订阅者轨道
   * @param streamId 流ID
   * @param trackId 轨道ID
   * @param receiver RTP接收器
   */
  public registerSubscriberTrack(streamId: string, trackId: string, receiver: RTCRtpReceiver): void {
    if (!this.subscribers.has(streamId)) {
      this.subscribers.set(streamId, new Map());
    }
    
    this.subscribers.get(streamId)!.set(trackId, receiver);
    logger.info(`注册订阅者轨道: ${streamId}/${trackId}`);
  }
  
  /**
   * 处理接收到的RTP包
   * @param streamId 流ID
   * @param trackId 轨道ID
   * @param packet RTP包
   */
  public handleRtpPacket(streamId: string, trackId: string, packet: RtpPacket): void {
    // 存储RTP包到缓冲区
    const bufferKey = `${streamId}/${trackId}`;
    const buffer = this.rtpBuffers.get(bufferKey) || [];
    buffer.push(packet);
    this.rtpBuffers.set(bufferKey, buffer);
    
    // 如果是关键帧且有请求关键帧的订阅者，通知他们
    if (packet.isKeyFrame && this.keyFrameRequested.has(bufferKey)) {
      this.keyFrameRequested.delete(bufferKey);
      this.emit('keyframe', streamId, trackId, packet);
    }
    
    // 转发RTP包给所有订阅者
    this.forwardRtpPacket(streamId, trackId, packet);
  }
  
  /**
   * 处理RTCP包
   * @param streamId 流ID
   * @param packet RTCP包
   */
  public handleRtcpPacket(streamId: string, packet: RtcpPacket): void {
    // 处理RTCP反馈
    if (packet.type === RtcpType.PSFB || packet.type === RtcpType.RTPFB) {
      // 处理关键帧请求
      if (this.isPictureLossIndication(packet) || this.isFullIntraRequest(packet)) {
        this.requestKeyFrame(streamId, packet.ssrc);
      }
    }
    
    // 转发RTCP包
    this.forwardRtcpPacket(streamId, packet);
  }
  
  /**
   * 请求关键帧
   * @param streamId 流ID
   * @param ssrc SSRC
   */
  private requestKeyFrame(streamId: string, ssrc: number): void {
    // 找到对应的轨道
    const publisherTracks = this.publishers.get(streamId);
    if (!publisherTracks) return;
    
    for (const [trackId, sender] of publisherTracks.entries()) {
      // 标记为请求了关键帧
      const bufferKey = `${streamId}/${trackId}`;
      this.keyFrameRequested.add(bufferKey);
      
      // 发送PLI (Picture Loss Indication)
      this.emit('request-keyframe', streamId, trackId, ssrc);
      logger.debug(`请求关键帧: ${streamId}/${trackId}`);
    }
  }
  
  /**
   * 转发RTP包给所有订阅者
   */
  private forwardRtpPacket(streamId: string, trackId: string, packet: RtpPacket): void {
    const subscribers = this.subscribers.get(streamId);
    if (!subscribers) return;
    
    for (const [subTrackId, receiver] of subscribers.entries()) {
      // 只转发给相同类型的轨道 (音频->音频, 视频->视频)
      if (subTrackId.startsWith(trackId.split('-')[0])) {
        this.emit('forward-rtp', streamId, subTrackId, packet);
      }
    }
  }
  
  /**
   * 转发RTCP包
   */
  private forwardRtcpPacket(streamId: string, packet: RtcpPacket): void {
    this.emit('forward-rtcp', streamId, packet);
  }
  
  /**
   * 清理过期的RTP包
   */
  private cleanupRtpBuffers(): void {
    const now = Date.now();
    
    for (const [key, buffer] of this.rtpBuffers.entries()) {
      // 移除超过缓冲区大小的包
      const newBuffer = buffer.filter(packet => 
        now - packet.timestamp < this.bufferSize
      );
      
      this.rtpBuffers.set(key, newBuffer);
    }
  }
  
  /**
   * 检查是否是PLI (Picture Loss Indication)
   */
  private isPictureLossIndication(packet: RtcpPacket): boolean {
    // 简化实现，实际应该解析RTCP包
    return packet.type === RtcpType.PSFB;
  }
  
  /**
   * 检查是否是FIR (Full Intra Request)
   */
  private isFullIntraRequest(packet: RtcpPacket): boolean {
    // 简化实现，实际应该解析RTCP包
    return packet.type === RtcpType.PSFB;
  }
}
