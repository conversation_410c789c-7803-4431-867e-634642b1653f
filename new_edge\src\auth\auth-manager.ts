import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { getLogger } from '../utils/logger';

const logger = getLogger();

/**
 * 用户信息
 */
export interface User {
  id: string;
  username: string;
  password: string;
  permissions: Permission[];
  createdAt: number;
  lastLogin?: number;
}

/**
 * 权限类型
 */
export interface Permission {
  resource: string; // 资源类型: stream, admin, api
  action: string;   // 操作: read, write, delete, create
  scope?: string;   // 作用域: 特定流ID或*表示所有
}

/**
 * JWT载荷
 */
export interface JwtPayload {
  userId: string;
  username: string;
  permissions: Permission[];
  iat: number;
  exp: number;
}

/**
 * 认证配置
 */
export interface AuthConfig {
  enabled: boolean;
  secret: string;
  tokenExpiry: number; // 秒
  bcryptRounds: number;
}

/**
 * 认证管理器
 */
export class AuthManager {
  private users = new Map<string, User>();
  private tokens = new Set<string>(); // 静态token列表
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
    
    // 创建默认管理员用户
    this.createDefaultAdmin();
  }

  /**
   * 添加静态token
   * @param token 静态token
   */
  addStaticToken(token: string): void {
    this.tokens.add(token);
    logger.info('添加静态token');
  }

  /**
   * 创建用户
   * @param username 用户名
   * @param password 密码
   * @param permissions 权限列表
   * @returns 用户信息
   */
  async createUser(username: string, password: string, permissions: Permission[]): Promise<User> {
    // 检查用户是否已存在
    for (const user of this.users.values()) {
      if (user.username === username) {
        throw new Error(`用户 ${username} 已存在`);
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, this.config.bcryptRounds);
    
    const user: User = {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      username,
      password: hashedPassword,
      permissions,
      createdAt: Date.now()
    };

    this.users.set(user.id, user);
    logger.info(`创建用户: ${username}`);
    return user;
  }

  /**
   * 用户登录
   * @param username 用户名
   * @param password 密码
   * @returns JWT token
   */
  async login(username: string, password: string): Promise<string> {
    // 查找用户
    let user: User | undefined;
    for (const u of this.users.values()) {
      if (u.username === username) {
        user = u;
        break;
      }
    }

    if (!user) {
      throw new Error('用户名或密码错误');
    }

    // 验证密码
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      throw new Error('用户名或密码错误');
    }

    // 更新最后登录时间
    user.lastLogin = Date.now();

    // 生成JWT token
    const payload: JwtPayload = {
      userId: user.id,
      username: user.username,
      permissions: user.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.config.tokenExpiry
    };

    const token = jwt.sign(payload, this.config.secret);
    logger.info(`用户登录: ${username}`);
    return token;
  }

  /**
   * 验证token
   * @param token JWT token或静态token
   * @returns JWT载荷或null
   */
  verifyToken(token: string): JwtPayload | null {
    // 如果认证未启用，返回管理员权限
    if (!this.config.enabled) {
      return {
        userId: 'admin',
        username: 'admin',
        permissions: [{ resource: '*', action: '*' }],
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600
      };
    }

    // 检查静态token
    if (this.tokens.has(token)) {
      return {
        userId: 'static',
        username: 'static',
        permissions: [{ resource: '*', action: '*' }],
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600
      };
    }

    // 验证JWT token
    try {
      const decoded = jwt.verify(token, this.config.secret) as JwtPayload;
      return decoded;
    } catch (error) {
      logger.debug(`Token验证失败: ${error}`);
      return null;
    }
  }

  /**
   * 检查权限
   * @param payload JWT载荷
   * @param resource 资源
   * @param action 操作
   * @param scope 作用域
   * @returns 是否有权限
   */
  checkPermission(payload: JwtPayload, resource: string, action: string, scope?: string): boolean {
    // 检查是否有通配符权限
    for (const permission of payload.permissions) {
      if (permission.resource === '*' && permission.action === '*') {
        return true;
      }
      
      if (permission.resource === resource || permission.resource === '*') {
        if (permission.action === action || permission.action === '*') {
          if (!scope || !permission.scope || permission.scope === '*' || permission.scope === scope) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * 创建默认管理员用户
   */
  private createDefaultAdmin(): void {
    const adminPermissions: Permission[] = [
      { resource: '*', action: '*' }
    ];

    // 使用简单密码进行开发测试
    this.createUser('admin', 'admin123', adminPermissions).catch(error => {
      logger.debug(`创建默认管理员失败: ${error.message}`);
    });
  }

  /**
   * 获取用户信息
   * @param userId 用户ID
   * @returns 用户信息
   */
  getUser(userId: string): User | undefined {
    return this.users.get(userId);
  }

  /**
   * 获取所有用户
   * @returns 用户列表
   */
  getAllUsers(): User[] {
    return Array.from(this.users.values()).map(user => ({
      ...user,
      password: '[HIDDEN]' // 隐藏密码
    })) as User[];
  }

  /**
   * 删除用户
   * @param userId 用户ID
   * @returns 是否成功删除
   */
  deleteUser(userId: string): boolean {
    const deleted = this.users.delete(userId);
    if (deleted) {
      logger.info(`删除用户: ${userId}`);
    }
    return deleted;
  }
}
