import express, { Request, Response } from 'express';
import { getLogger } from '../utils/logger';
import { StreamManager } from '../stream/manager';
import { PeerConnectionManager } from '../webrtc/peer-connection';
import { getSystemMetrics } from '../utils/metrics';

const logger = getLogger();
const router = express.Router();

export function setupApiRoutes(
  streamManager: StreamManager,
  peerManager: PeerConnectionManager
): express.Router {
  
  // 获取所有流
  router.get('/streams', (req: Request, res: Response) => {
    try {
      const streams = streamManager.getAllStreams();
      res.json({
        count: streams.length,
        streams
      });
    } catch (error) {
      logger.error(`获取流列表失败: ${error.message}`);
      res.status(500).json({ error: error.message });
    }
  });

  // 获取单个流信息
  router.get('/streams/:streamId', (req: Request, res: Response) => {
    try {
      const { streamId } = req.params;
      const stream = streamManager.getStream(streamId);
      
      if (!stream) {
        return res.status(404).json({ error: '流不存在' });
      }
      
      res.json(stream);
    } catch (error) {
      logger.error(`获取流信息失败: ${error.message}`);