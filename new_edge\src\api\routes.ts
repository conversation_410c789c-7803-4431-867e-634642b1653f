import express, { Request, Response } from 'express';
import { getLogger } from '../utils/logger';
import { StreamManager } from '../stream/manager';
import { CascadeManager } from '../cascade/cascade-manager';
import { AuthManager } from '../auth/auth-manager';

const logger = getLogger();

export function setupApiRoutes(
  streamManager: StreamManager,
  cascadeManager: CascadeManager,
  authManager: AuthManager
): express.Router {
  const router = express.Router();

  // 流管理API
  const streamRouter = express.Router();

  // 获取所有流
  streamRouter.get('/', (req: Request, res: Response) => {
    try {
      const streams = streamManager.getAllStreams();
      res.json({
        count: streams.length,
        streams
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`获取流列表失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 获取特定流
  streamRouter.get('/:streamId', (req: Request, res: Response) => {
    const { streamId } = req.params;

    try {
      const stream = streamManager.getStream(streamId);

      if (!stream) {
        return res.status(404).json({ error: '找不到流' });
      }

      res.json(stream);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`获取流失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 创建流
  streamRouter.post('/', (req: Request, res: Response) => {
    const { streamId, metadata } = req.body;

    if (!streamId) {
      return res.status(400).json({ error: '流ID不能为空' });
    }

    try {
      const stream = streamManager.createStream(streamId, metadata);
      res.status(201).json(stream);
      logger.info(`API创建流: ${streamId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建流失败: ${errorMessage}`);
      res.status(400).json({ error: errorMessage });
    }
  });

  // 删除流
  streamRouter.delete('/:streamId', (req: Request, res: Response) => {
    const { streamId } = req.params;

    try {
      const success = streamManager.deleteStream(streamId);

      if (!success) {
        return res.status(404).json({ error: '找不到流' });
      }

      res.status(204).end();
      logger.info(`API删除流: ${streamId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`删除流失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 获取流会话
  streamRouter.get('/:streamId/sessions', (req: Request, res: Response) => {
    const { streamId } = req.params;

    try {
      const sessions = streamManager.getStreamSessions(streamId);
      res.json(sessions);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`获取流会话失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 级联API
  const cascadeRouter = express.Router();

  // 获取所有节点
  cascadeRouter.get('/nodes', (req: Request, res: Response) => {
    try {
      const nodes = cascadeManager.getAllNodes();
      res.json(nodes);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`获取级联节点失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 添加节点
  cascadeRouter.post('/nodes', (req: Request, res: Response) => {
    const { id, alias, url, token } = req.body;

    if (!id || !alias || !url) {
      return res.status(400).json({ error: '节点ID、别名和URL不能为空' });
    }

    try {
      cascadeManager.addNode({ id, alias, url, token });
      res.status(201).json({ id, alias, url });
      logger.info(`API添加节点: ${alias} (${url})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`添加节点失败: ${errorMessage}`);
      res.status(400).json({ error: errorMessage });
    }
  });

  // 删除节点
  cascadeRouter.delete('/nodes/:nodeId', (req: Request, res: Response) => {
    const { nodeId } = req.params;

    try {
      const success = cascadeManager.removeNode(nodeId);

      if (!success) {
        return res.status(404).json({ error: '找不到节点' });
      }

      res.status(204).end();
      logger.info(`API删除节点: ${nodeId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`删除节点失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 创建级联推流
  cascadeRouter.post('/push/:streamId', async (req: Request, res: Response) => {
    const { streamId } = req.params;
    const { targetUrl, token } = req.body;

    if (!targetUrl) {
      return res.status(400).json({ error: '目标URL不能为空' });
    }

    try {
      await cascadeManager.handleCascadePush(streamId, { targetUrl, token });
      res.status(201).json({ status: 'success', streamId, targetUrl });
      logger.info(`API创建级联推流: ${streamId} -> ${targetUrl}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建级联推流失败: ${errorMessage}`);
      res.status(400).json({ error: errorMessage });
    }
  });

  // 创建级联拉流
  cascadeRouter.post('/pull/:streamId', async (req: Request, res: Response) => {
    const { streamId } = req.params;
    const { sourceUrl, token } = req.body;

    if (!sourceUrl) {
      return res.status(400).json({ error: '源URL不能为空' });
    }

    try {
      await cascadeManager.handleCascadePull(streamId, { sourceUrl, token });
      res.status(201).json({ status: 'success', streamId, sourceUrl });
      logger.info(`API创建级联拉流: ${streamId} <- ${sourceUrl}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建级联拉流失败: ${errorMessage}`);
      res.status(400).json({ error: errorMessage });
    }
  });

  // 认证API
  const authRouter = express.Router();

  // 用户登录
  authRouter.post('/login', async (req: Request, res: Response) => {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    try {
      const token = await authManager.login(username, password);
      res.json({ token });
      logger.info(`用户登录成功: ${username}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn(`用户登录失败: ${username} - ${errorMessage}`);
      res.status(401).json({ error: errorMessage });
    }
  });

  // 获取所有用户
  authRouter.get('/users', (req: Request, res: Response) => {
    try {
      const users = authManager.getAllUsers();
      res.json(users);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`获取用户列表失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 创建用户
  authRouter.post('/users', async (req: Request, res: Response) => {
    const { username, password, permissions } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    try {
      const user = await authManager.createUser(username, password, permissions || []);
      res.status(201).json({
        id: user.id,
        username: user.username,
        permissions: user.permissions,
        createdAt: user.createdAt
      });
      logger.info(`创建用户: ${username}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建用户失败: ${errorMessage}`);
      res.status(400).json({ error: errorMessage });
    }
  });

  // 删除用户
  authRouter.delete('/users/:userId', (req: Request, res: Response) => {
    const { userId } = req.params;

    try {
      const success = authManager.deleteUser(userId);

      if (!success) {
        return res.status(404).json({ error: '找不到用户' });
      }

      res.status(204).end();
      logger.info(`删除用户: ${userId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`删除用户失败: ${errorMessage}`);
      res.status(500).json({ error: errorMessage });
    }
  });

  // 注册子路由
  router.use('/streams', streamRouter);
  router.use('/cascade', cascadeRouter);
  router.use('/auth', authRouter);

  return router;
}