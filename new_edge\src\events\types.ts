export interface NodeMetrics {
  cpu: number;
  memory: number;
  network: {
    rx: number;
    tx: number;
  };
}

export interface EventBody {
  metrics: NodeMetrics;
  event: StreamEvent;
}

export type StreamEventType = 
  | 'StreamUp' | 'StreamDown'
  | 'PublishUp' | 'PublishDown'
  | 'SubscribeUp' | 'SubscribeDown'
  | 'ReforwardUp' | 'ReforwardDown';

export interface StreamEvent {
  type: StreamEventType;
  stream: StreamInfo;
}