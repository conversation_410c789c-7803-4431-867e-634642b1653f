#!/usr/bin/env node

/**
 * Live777 Edge Server API测试脚本
 */

const http = require('http');

const BASE_URL = 'http://localhost:8080';

// 简单的HTTP请求函数
function request(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试函数
async function runTests() {
  console.log('🚀 开始测试Live777 Edge Server API...\n');

  try {
    // 1. 健康检查
    console.log('1. 测试健康检查...');
    const health = await request('GET', '/health');
    console.log(`   状态: ${health.status}`);
    console.log(`   响应: ${JSON.stringify(health.data)}\n`);

    // 2. 获取指标
    console.log('2. 测试指标端点...');
    const metrics = await request('GET', '/metrics');
    console.log(`   状态: ${metrics.status}`);
    console.log(`   指标数据长度: ${metrics.data ? metrics.data.length : 0} 字符\n`);

    // 3. 获取所有流
    console.log('3. 测试获取所有流...');
    const streams = await request('GET', '/api/streams');
    console.log(`   状态: ${streams.status}`);
    console.log(`   流数量: ${streams.data ? streams.data.count : 0}\n`);

    // 4. 创建流
    console.log('4. 测试创建流...');
    const createStream = await request('POST', '/api/streams', {
      streamId: 'test-stream-' + Date.now(),
      metadata: { description: 'API测试流' }
    });
    console.log(`   状态: ${createStream.status}`);
    if (createStream.data) {
      console.log(`   流ID: ${createStream.data.id}\n`);
    }

    // 5. 获取级联节点
    console.log('5. 测试获取级联节点...');
    const nodes = await request('GET', '/api/cascade/nodes');
    console.log(`   状态: ${nodes.status}`);
    console.log(`   节点数量: ${nodes.data ? nodes.data.length : 0}\n`);

    // 6. 测试认证登录
    console.log('6. 测试用户登录...');
    const login = await request('POST', '/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    console.log(`   状态: ${login.status}`);
    if (login.data && login.data.token) {
      console.log(`   Token获取成功: ${login.data.token.substring(0, 20)}...\n`);
    } else {
      console.log(`   登录失败: ${JSON.stringify(login.data)}\n`);
    }

    // 7. 测试WHIP协议（模拟SDP）
    console.log('7. 测试WHIP协议...');
    const mockSdp = `v=0
o=- 123456789 123456789 IN IP4 127.0.0.1
s=-
t=0 0
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 127.0.0.1
a=rtcp:9 IN IP4 127.0.0.1
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=sendrecv
a=rtcp-mux
a=rtpmap:96 VP8/90000`;

    const whip = await request('POST', '/whip/test-stream', mockSdp, {
      'Content-Type': 'application/sdp'
    });
    console.log(`   状态: ${whip.status}`);
    if (whip.status === 201) {
      console.log(`   WHIP推流成功\n`);
    } else {
      console.log(`   WHIP推流失败: ${whip.data}\n`);
    }

    console.log('✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}
