name: "liveman"
arch: "${NFPM_ARCH}"
platform: "linux"
version: "${NFPM_VERSION}"
release: "${NFPM_RELEASE}"
prerelease: "${NFPM_PRERELEASE}"
section: "utility"
priority: "optional"
maintainer: "BinBat Ltd <<EMAIL>>"
description: |
  A very simple, high performance, edge WebRTC SFU.
  Live777 cluster manager controller
vendor: "BinBat"
homepage: "http://live777.binbat.com"
license: "MPL-2.0"
contents:
  - src: ./target/${NFPM_TARGET}/release/liveman
    dst: /usr/bin/liveman
  - src: ./conf/liveman.service
    dst: /usr/lib/systemd/system/liveman.service
  - src: ./conf/liveman.toml
    dst: /etc/live777/liveman.toml
    type: config

