import { Request, Response } from 'express';

export class WhipHandler {
  async handleOffer(req: Request, res: Response): Promise<void> {
    const { streamId } = req.params;
    const sdpOffer = req.body;

    if (req.headers['content-type'] !== 'application/sdp') {
      res.status(400).json({ error: 'Content-Type must be application/sdp' });
      return;
    }

    try {
      const peer = await this.createPublisher(streamId, sdpOffer);
      const answer = await peer.createAnswer();
      
      res.setHeader('Content-Type', 'application/sdp');
      res.setHeader('Location', `/whip/${streamId}`);
      res.status(201).send(answer.sdp);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  private async createPublisher(streamId: string, offer: string): Promise<RTCPeerConnection> {
    // 实现发布者创建逻辑
    const peer = new RTCPeerConnection();
    await peer.setRemoteDescription({ type: 'offer', sdp: offer });
    return peer;
  }
}