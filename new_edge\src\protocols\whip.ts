import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { getLogger } from '../utils/logger';
import { StreamManager } from '../stream/manager';
import { ClientInfo } from '../types';

const logger = getLogger();

export class WhipHandler {
  constructor(private streamManager: StreamManager) {}

  /**
   * 处理WHIP请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleRequest(req: Request, res: Response): Promise<void> {
    const { streamId } = req.params;

    if (!streamId) {
      res.status(400).send('Missing streamId');
      return;
    }

    // 检查Content-Type
    if (req.headers['content-type'] !== 'application/sdp') {
      res.status(415).send('Content-Type must be application/sdp');
      return;
    }

    const sdpOffer = req.body;
    if (!sdpOffer) {
      res.status(400).send('Missing SDP offer');
      return;
    }

    try {
      // 创建客户端信息
      const clientId = uuidv4();
      const clientInfo: ClientInfo = {
        id: clientId,
        ip: req.ip || 'unknown',
        userAgent: req.headers['user-agent'],
        startTime: Date.now(),
        type: 'publisher'
      };

      // 处理发布请求
      const { sessionId, answer } = await this.streamManager.handlePublish(
        streamId,
        clientInfo,
        sdpOffer
      );

      // 设置响应头
      res.setHeader('Content-Type', 'application/sdp');
      res.setHeader('Location', `/whip/${streamId}/${sessionId}`);
      res.setHeader('Link', '<https://www.ietf.org/archive/id/draft-ietf-wish-whip-00.html#name-ice-server-configuration>; rel="ice-server"');

      // 返回SDP应答
      res.status(201).send(answer);

      logger.info(`WHIP发布成功: ${streamId} (${sessionId})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`WHIP处理错误: ${errorMessage}`);
      res.status(500).send(`Error: ${errorMessage}`);
    }
  }

  /**
   * 处理会话删除请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleSessionDelete(req: Request, res: Response): Promise<void> {
    const { streamId, sessionId } = req.params;

    if (!streamId || !sessionId) {
      res.status(400).send('Missing streamId or sessionId');
      return;
    }

    try {
      const success = this.streamManager.removeSession(sessionId);
      if (success) {
        res.status(204).send();
        logger.info(`WHIP会话删除成功: ${streamId}/${sessionId}`);
      } else {
        res.status(404).send('Session not found');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`WHIP会话删除错误: ${errorMessage}`);
      res.status(500).send(`Error: ${errorMessage}`);
    }
  }
}