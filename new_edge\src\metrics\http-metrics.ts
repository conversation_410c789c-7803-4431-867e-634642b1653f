import { Request, Response, NextFunction } from 'express';
import { MetricsManager } from './metrics-manager';

/**
 * HTTP指标中间件
 * @param metricsManager 指标管理器
 * @returns Express中间件
 */
export function httpMetricsMiddleware(metricsManager: MetricsManager) {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // 监听响应完成事件
    res.on('finish', () => {
      const duration = (Date.now() - startTime) / 1000;
      const method = req.method;
      const path = normalizeHttpPath(req.path);
      const status = res.statusCode;
      
      // 记录HTTP请求指标
      metricsManager.recordHttpRequest(method, path, status, duration);
    });
    
    next();
  };
}

/**
 * 标准化HTTP路径，将动态参数替换为占位符
 * @param path 原始路径
 * @returns 标准化后的路径
 */
function normalizeHttpPath(path: string): string {
  // 替换常见的动态参数
  return path
    .replace(/\/whip\/[^\/]+/g, '/whip/:streamId')
    .replace(/\/whep\/[^\/]+/g, '/whep/:streamId')
    .replace(/\/streams\/[^\/]+/g, '/streams/:streamId')
    .replace(/\/session\/[^\/]+\/[^\/]+/g, '/session/:streamId/:sessionId')
    .replace(/\/api\/streams\/[^\/]+/g, '/api/streams/:streamId')
    .replace(/\/[a-f0-9-]{36}/g, '/:id') // UUID
    .replace(/\/\d+/g, '/:id'); // 数字ID
}
