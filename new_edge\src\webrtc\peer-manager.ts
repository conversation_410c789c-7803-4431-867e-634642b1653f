import { EventEmitter } from 'events';

export class PeerManager extends EventEmitter {
  private peers = new Map<string, RTCPeerConnection>();
  private config: RTCConfiguration;

  constructor() {
    super();
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    };
  }

  async createPeer(streamId: string): Promise<RTCPeerConnection> {
    const peer = new RTCPeerConnection(this.config);
    
    peer.oniceconnectionstatechange = () => {
      this.emit('ice-state-change', streamId, peer.iceConnectionState);
    };

    this.peers.set(streamId, peer);
    return peer;
  }

  removePeer(streamId: string): void {
    const peer = this.peers.get(streamId);
    if (peer) {
      peer.close();
      this.peers.delete(streamId);
    }
  }
}