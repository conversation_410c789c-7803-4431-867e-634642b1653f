import { Request, Response, NextFunction } from 'express';
import { AuthManager } from './auth-manager';
import { getLogger } from '../utils/logger';

const logger = getLogger();

/**
 * 认证中间件
 * @param authManager 认证管理器
 * @returns Express中间件
 */
export function authMiddleware(authManager: AuthManager) {
  return (req: Request, res: Response, next: NextFunction) => {
    // 如果认证未启用，直接通过
    if (!authManager.verifyToken('')) {
      return next();
    }

    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: '未提供认证信息' });
    }

    // 解析token
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;

    // 验证token
    const payload = authManager.verifyToken(token);
    if (!payload) {
      return res.status(401).json({ error: '无效的认证信息' });
    }

    // 将用户信息添加到请求对象
    (req as any).user = payload;
    
    next();
  };
}

/**
 * 权限检查中间件
 * @param resource 资源
 * @param action 操作
 * @param scopeExtractor 作用域提取函数
 * @returns Express中间件
 */
export function permissionMiddleware(
  authManager: AuthManager,
  resource: string,
  action: string,
  scopeExtractor?: (req: Request) => string
) {
  return (req: Request, res: Response, next: NextFunction) => {
    // 获取用户信息
    const user = (req as any).user;
    if (!user) {
      return res.status(401).json({ error: '未认证' });
    }

    // 提取作用域
    const scope = scopeExtractor ? scopeExtractor(req) : undefined;

    // 检查权限
    if (!authManager.checkPermission(user, resource, action, scope)) {
      logger.warn(`权限拒绝: ${user.username} 尝试访问 ${resource}:${action}:${scope || '*'}`);
      return res.status(403).json({ error: '权限不足' });
    }

    next();
  };
}

/**
 * WHIP/WHEP协议认证中间件
 * @param authManager 认证管理器
 * @returns Express中间件
 */
export function whipWhepAuthMiddleware(authManager: AuthManager) {
  return (req: Request, res: Response, next: NextFunction) => {
    // 如果认证未启用，直接通过
    if (!authManager.verifyToken('')) {
      return next();
    }

    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // 解析token
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;

    // 验证token
    const payload = authManager.verifyToken(token);
    if (!payload) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // 检查权限
    const { path } = req;
    const streamId = req.params.streamId;
    
    // 确定资源和操作
    let resource = 'stream';
    let action = 'read';
    
    if (path.includes('/whip/')) {
      action = 'write';
    } else if (path.includes('/whep/')) {
      action = 'read';
    }

    // 检查权限
    if (!authManager.checkPermission(payload, resource, action, streamId)) {
      logger.warn(`WHIP/WHEP权限拒绝: ${payload.username} 尝试访问 ${resource}:${action}:${streamId}`);
      return res.status(403).json({ error: 'Forbidden' });
    }

    // 将用户信息添加到请求对象
    (req as any).user = payload;
    
    next();
  };
}
