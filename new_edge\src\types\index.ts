// 基础配置类型
export interface ServerConfig {
  http: {
    listen: string;
    public: string;
    cors?: boolean;
  };
  webrtc: {
    iceServers: RTCIceServer[];
    maxBitrate?: number;
  };
  log: {
    level: 'TRACE' | 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    file?: string;
  };
  cascade: {
    mode: 'auto' | 'manual';
    nodes?: string[];
  };
  auth?: {
    enabled: boolean;
    secret?: string;
    tokenExpiry?: number;
  };
}

// 流信息类型
export interface StreamInfo {
  id: string;
  name?: string;
  created: number;
  publish: {
    active: boolean;
    clientId?: string;
    startTime?: number;
  };
  subscribers: {
    count: number;
    clients: ClientInfo[];
  };
  metadata?: Record<string, any>;
}

// 客户端信息
export interface ClientInfo {
  id: string;
  ip: string;
  userAgent?: string;
  startTime: number;
  type: 'publisher' | 'subscriber';
}

// 会话信息
export interface SessionInfo {
  id: string;
  streamId: string;
  clientInfo: ClientInfo;
  peerConnection?: RTCPeerConnection;
}

// 节点指标
export interface NodeMetrics {
  cpu: number;
  memory: number;
  network: {
    rx: number;
    tx: number;
  };
  streams: number;
  clients: number;
  uptime: number;
}

// 事件类型
export type EventType = 
  | 'stream:created' 
  | 'stream:deleted'
  | 'publish:start' 
  | 'publish:stop'
  | 'subscribe:start' 
  | 'subscribe:stop'
  | 'error';

// 事件数据
export interface EventData {
  type: EventType;
  timestamp: number;
  streamId?: string;
  clientId?: string;
  data?: any;
}