import { getLogger } from '../utils/logger';
import { StreamInfo, ClientInfo } from '../types';
import { PeerConnectionManager } from '../webrtc/peer-connection';

const logger = getLogger();

export interface StreamSession {
  id: string;
  streamId: string;
  clientInfo: ClientInfo;
  type: 'publisher' | 'subscriber';
  createdAt: number;
  active: boolean;
}

export class StreamManager {
  private streams = new Map<string, StreamInfo>();
  private sessions = new Map<string, StreamSession>();
  private streamSessions = new Map<string, Set<string>>();
  private peerManager: PeerConnectionManager;

  // 配置选项
  private autoCreateOnPublish = true;
  private autoCreateOnSubscribe = true;
  private autoDeleteTimeout = 30000; // 30秒后自动删除空流

  constructor(iceServers?: RTCIceServer[]) {
    this.peerManager = new PeerConnectionManager(iceServers);

    // 设置对等连接管理器事件监听
    this.setupPeerManagerEvents();

    // 启动自动清理任务
    this.startAutoCleanup();
  }

  /**
   * 创建流
   * @param streamId 流ID
   * @param metadata 元数据
   * @returns 流信息
   */
  createStream(streamId: string, metadata?: Record<string, any>): StreamInfo {
    if (this.streams.has(streamId)) {
      throw new Error(`流 ${streamId} 已存在`);
    }

    const stream: StreamInfo = {
      id: streamId,
      created: Date.now(),
      publish: {
        active: false
      },
      subscribers: {
        count: 0,
        clients: []
      },
      metadata
    };

    this.streams.set(streamId, stream);
    this.streamSessions.set(streamId, new Set());

    logger.info(`创建流: ${streamId}`);
    return stream;
  }

  /**
   * 删除流
   * @param streamId 流ID
   * @returns 是否成功删除
   */
  deleteStream(streamId: string): boolean {
    const stream = this.streams.get(streamId);
    if (!stream) {
      return false;
    }

    // 关闭所有相关会话
    const sessionIds = this.streamSessions.get(streamId);
    if (sessionIds) {
      for (const sessionId of sessionIds) {
        this.removeSession(sessionId);
      }
    }

    this.streams.delete(streamId);
    this.streamSessions.delete(streamId);

    logger.info(`删除流: ${streamId}`);
    return true;
  }

  /**
   * 获取流信息
   * @param streamId 流ID
   * @returns 流信息
   */
  getStream(streamId: string): StreamInfo | undefined {
    return this.streams.get(streamId);
  }

  /**
   * 获取所有流
   * @returns 流信息数组
   */
  getAllStreams(): StreamInfo[] {
    return Array.from(this.streams.values());
  }

  /**
   * 处理发布请求
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   * @returns 会话ID和SDP answer
   */
  async handlePublish(streamId: string, clientInfo: ClientInfo, offer: string): Promise<{ sessionId: string, answer: string }> {
    // 自动创建流
    if (!this.streams.has(streamId) && this.autoCreateOnPublish) {
      this.createStream(streamId);
    }

    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`流 ${streamId} 不存在`);
    }

    // 检查是否已有发布者
    if (stream.publish.active) {
      throw new Error(`流 ${streamId} 已有发布者`);
    }

    // 创建发布者连接
    const result = await this.peerManager.createPublisher(streamId, clientInfo, offer);

    // 创建会话
    const session: StreamSession = {
      id: result.sessionId,
      streamId,
      clientInfo,
      type: 'publisher',
      createdAt: Date.now(),
      active: true
    };

    this.sessions.set(result.sessionId, session);
    this.streamSessions.get(streamId)!.add(result.sessionId);

    // 更新流状态
    stream.publish.active = true;
    stream.publish.clientId = clientInfo.id;
    stream.publish.startTime = Date.now();

    logger.info(`处理发布请求: ${streamId} (${result.sessionId})`);
    return result;
  }

  /**
   * 处理订阅请求
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   * @returns 会话ID和SDP answer
   */
  async handleSubscribe(streamId: string, clientInfo: ClientInfo, offer: string): Promise<{ sessionId: string, answer: string }> {
    // 自动创建流
    if (!this.streams.has(streamId) && this.autoCreateOnSubscribe) {
      this.createStream(streamId);
    }

    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`流 ${streamId} 不存在`);
    }

    // 检查是否有发布者
    if (!stream.publish.active) {
      throw new Error(`流 ${streamId} 没有活跃的发布者`);
    }

    // 创建订阅者连接
    const result = await this.peerManager.createSubscriber(streamId, clientInfo, offer);

    // 创建会话
    const session: StreamSession = {
      id: result.sessionId,
      streamId,
      clientInfo,
      type: 'subscriber',
      createdAt: Date.now(),
      active: true
    };

    this.sessions.set(result.sessionId, session);
    this.streamSessions.get(streamId)!.add(result.sessionId);

    // 更新流状态
    stream.subscribers.count++;
    stream.subscribers.clients.push(clientInfo);

    logger.info(`处理订阅请求: ${streamId} (${result.sessionId})`);
    return result;
  }

  /**
   * 移除会话
   * @param sessionId 会话ID
   * @returns 是否成功移除
   */
  removeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // 关闭对等连接
    this.peerManager.closeSession(sessionId);

    // 从流会话列表中移除
    const streamSessions = this.streamSessions.get(session.streamId);
    if (streamSessions) {
      streamSessions.delete(sessionId);
    }

    // 更新流状态
    const stream = this.streams.get(session.streamId);
    if (stream) {
      if (session.type === 'publisher') {
        stream.publish.active = false;
        stream.publish.clientId = undefined;
        stream.publish.startTime = undefined;
      } else {
        stream.subscribers.count = Math.max(0, stream.subscribers.count - 1);
        stream.subscribers.clients = stream.subscribers.clients.filter(
          client => client.id !== session.clientInfo.id
        );
      }
    }

    this.sessions.delete(sessionId);
    logger.info(`移除会话: ${sessionId} from stream ${session.streamId}`);
    return true;
  }

  /**
   * 设置对等连接管理器事件监听
   */
  private setupPeerManagerEvents(): void {
    this.peerManager.on('publisher:created', (streamId: string, sessionId: string) => {
      logger.debug(`发布者创建: ${streamId}/${sessionId}`);
    });

    this.peerManager.on('subscriber:created', (streamId: string, sessionId: string) => {
      logger.debug(`订阅者创建: ${streamId}/${sessionId}`);
    });

    this.peerManager.on('connection:closed', (streamId: string, sessionId: string) => {
      logger.debug(`连接关闭: ${streamId}/${sessionId}`);
      this.removeSession(sessionId);
    });
  }

  /**
   * 启动自动清理任务
   */
  private startAutoCleanup(): void {
    setInterval(() => {
      this.cleanupEmptyStreams();
    }, this.autoDeleteTimeout);
  }

  /**
   * 清理空流
   */
  private cleanupEmptyStreams(): void {
    const now = Date.now();

    for (const [streamId, stream] of this.streams.entries()) {
      const sessions = this.streamSessions.get(streamId);
      const hasActiveSessions = sessions && sessions.size > 0;

      // 如果流没有活跃会话且创建时间超过清理阈值，则删除
      if (!hasActiveSessions && (now - stream.created) > this.autoDeleteTimeout) {
        this.deleteStream(streamId);
        logger.info(`自动清理空流: ${streamId}`);
      }
    }
  }

  /**
   * 获取会话信息
   * @param sessionId 会话ID
   * @returns 会话信息
   */
  getSession(sessionId: string): StreamSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 获取流的所有会话
   * @param streamId 流ID
   * @returns 会话信息数组
   */
  getStreamSessions(streamId: string): StreamSession[] {
    const sessionIds = this.streamSessions.get(streamId);
    if (!sessionIds) {
      return [];
    }

    const sessions: StreamSession[] = [];
    for (const sessionId of sessionIds) {
      const session = this.sessions.get(sessionId);
      if (session) {
        sessions.push(session);
      }
    }

    return sessions;
  }
}