export class StreamManager {
  private streams = new Map<string, StreamInfo>();
  private sessions = new Map<string, Set<string>>();

  createStream(streamId: string): StreamInfo {
    const stream: StreamInfo = {
      stream: streamId,
      publish: 0,
      subscribe: 0,
      reforward: 0
    };
    
    this.streams.set(streamId, stream);
    this.sessions.set(streamId, new Set());
    return stream;
  }

  addSession(streamId: string, sessionId: string): void {
    const sessions = this.sessions.get(streamId);
    if (sessions) {
      sessions.add(sessionId);
    }
  }

  removeSession(streamId: string, sessionId: string): boolean {
    const sessions = this.sessions.get(streamId);
    if (sessions) {
      return sessions.delete(sessionId);
    }
    return false;
  }

  getStreams(): StreamInfo[] {
    return Array.from(this.streams.values());
  }
}