#!/bin/bash

# Live777 Edge Server 启动脚本

set -e

echo "🚀 启动Live777 Edge Server..."

# 检查Node.js版本
NODE_VERSION=$(node --version 2>/dev/null || echo "未安装")
if [[ "$NODE_VERSION" == "未安装" ]]; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 16+版本"
    exit 1
fi

echo "✅ Node.js版本: $NODE_VERSION"

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 构建项目
echo "🔨 构建项目..."
npm run build

# 设置环境变量（如果未设置）
export HTTP_LISTEN=${HTTP_LISTEN:-"0.0.0.0:8080"}
export HTTP_PUBLIC=${HTTP_PUBLIC:-"http://localhost:8080"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
export AUTH_ENABLED=${AUTH_ENABLED:-"false"}

echo "⚙️  配置信息:"
echo "   监听地址: $HTTP_LISTEN"
echo "   公共地址: $HTTP_PUBLIC"
echo "   日志级别: $LOG_LEVEL"
echo "   认证启用: $AUTH_ENABLED"
echo ""

# 启动服务器
echo "🎯 启动服务器..."
npm start
