import { getLogger } from '../utils/logger';
import { VideoCodec, AudioCodec } from './media-engine';

const logger = getLogger();

/**
 * SDP处理工具 - 负责SDP的解析、修改和生成
 */
export class SdpUtils {
  /**
   * 解析SDP中的媒体部分
   * @param sdp SDP字符串
   * @returns 媒体部分数组
   */
  public static parseMediaSections(sdp: string): string[] {
    const mediaSections: string[] = [];
    const lines = sdp.split('\r\n');
    let currentSection = '';
    let inMediaSection = false;

    for (const line of lines) {
      if (line.startsWith('m=')) {
        if (inMediaSection) {
          mediaSections.push(currentSection);
        }
        currentSection = line + '\r\n';
        inMediaSection = true;
      } else if (inMediaSection) {
        currentSection += line + '\r\n';
      }
    }

    if (inMediaSection) {
      mediaSections.push(currentSection);
    }

    return mediaSections;
  }

  /**
   * 获取SDP中的会话部分
   * @param sdp SDP字符串
   * @returns 会话部分
   */
  public static getSessionPart(sdp: string): string {
    const lines = sdp.split('\r\n');
    let sessionPart = '';
    
    for (const line of lines) {
      if (line.startsWith('m=')) {
        break;
      }
      sessionPart += line + '\r\n';
    }
    
    return sessionPart;
  }

  /**
   * 修改SDP以支持特定的编解码器
   * @param sdp SDP字符串
   * @param preferredVideoCodecs 首选视频编解码器
   * @param preferredAudioCodecs 首选音频编解码器
   * @returns 修改后的SDP
   */
  public static filterCodecs(
    sdp: string, 
    preferredVideoCodecs: VideoCodec[] = [VideoCodec.VP8, VideoCodec.H264, VideoCodec.VP9, VideoCodec.AV1],
    preferredAudioCodecs: AudioCodec[] = [AudioCodec.OPUS, AudioCodec.G722]
  ): string {
    const sessionPart = this.getSessionPart(sdp);
    const mediaSections = this.parseMediaSections(sdp);
    const filteredSections: string[] = [];
    
    for (const section of mediaSections) {
      if (section.startsWith('m=video')) {
        filteredSections.push(
          this.filterVideoCodecs(section, preferredVideoCodecs)
        );
      } else if (section.startsWith('m=audio')) {
        filteredSections.push(
          this.filterAudioCodecs(section, preferredAudioCodecs)
        );
      } else {
        filteredSections.push(section);
      }
    }
    
    return sessionPart + filteredSections.join('');
  }
  
  /**
   * 过滤视频编解码器
   * @param mediaSection 媒体部分
   * @param preferredCodecs 首选编解码器
   * @returns 过滤后的媒体部分
   */
  private static filterVideoCodecs(mediaSection: string, preferredCodecs: VideoCodec[]): string {
    const lines = mediaSection.split('\r\n');
    const rtpmapLines: string[] = [];
    const fmtpLines: string[] = [];
    const rtcpFbLines: string[] = [];
    const otherLines: string[] = [];
    const payloadTypes: string[] = [];
    const codecMap = new Map<string, string>();
    
    // 提取所有编解码器相关的行
    for (const line of lines) {
      if (line.startsWith('a=rtpmap:')) {
        rtpmapLines.push(line);
        const parts = line.split(' ');
        const pt = parts[0].split(':')[1];
        const codec = parts[1].split('/')[0].toUpperCase();
        codecMap.set(pt, codec);
      } else if (line.startsWith('a=fmtp:')) {
        fmtpLines.push(line);
      } else if (line.startsWith('a=rtcp-fb:')) {
        rtcpFbLines.push(line);
      } else if (line.startsWith('m=')) {
        // 保存媒体行，稍后会修改
        otherLines.push(line);
      } else {
        otherLines.push(line);
      }
    }
    
    // 按照首选编解码器排序
    for (const codec of preferredCodecs) {
      for (const [pt, codecName] of codecMap.entries()) {
        if (codecName === codec) {
          payloadTypes.push(pt);
        }
      }
    }
    
    // 重建媒体行
    let mediaLine = '';
    for (const line of otherLines) {
      if (line.startsWith('m=')) {
        const parts = line.split(' ');
        mediaLine = `${parts[0]} ${parts[1]} ${parts[2]}`;
        for (const pt of payloadTypes) {
          mediaLine += ` ${pt}`;
        }
      }
    }
    
    // 重建媒体部分
    let result = mediaLine + '\r\n';
    for (const line of otherLines) {
      if (!line.startsWith('m=')) {
        result += line + '\r\n';
      }
    }
    
    // 添加编解码器相关的行
    for (const pt of payloadTypes) {
      // 添加rtpmap行
      for (const line of rtpmapLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
      
      // 添加fmtp行
      for (const line of fmtpLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
      
      // 添加rtcp-fb行
      for (const line of rtcpFbLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
    }
    
    return result;
  }
  
  /**
   * 过滤音频编解码器
   * @param mediaSection 媒体部分
   * @param preferredCodecs 首选编解码器
   * @returns 过滤后的媒体部分
   */
  private static filterAudioCodecs(mediaSection: string, preferredCodecs: AudioCodec[]): string {
    // 实现类似于filterVideoCodecs的逻辑
    // 为简化代码，这里使用相同的实现
    const lines = mediaSection.split('\r\n');
    const rtpmapLines: string[] = [];
    const fmtpLines: string[] = [];
    const rtcpFbLines: string[] = [];
    const otherLines: string[] = [];
    const payloadTypes: string[] = [];
    const codecMap = new Map<string, string>();
    
    // 提取所有编解码器相关的行
    for (const line of lines) {
      if (line.startsWith('a=rtpmap:')) {
        rtpmapLines.push(line);
        const parts = line.split(' ');
        const pt = parts[0].split(':')[1];
        const codec = parts[1].split('/')[0].toUpperCase();
        codecMap.set(pt, codec);
      } else if (line.startsWith('a=fmtp:')) {
        fmtpLines.push(line);
      } else if (line.startsWith('a=rtcp-fb:')) {
        rtcpFbLines.push(line);
      } else if (line.startsWith('m=')) {
        // 保存媒体行，稍后会修改
        otherLines.push(line);
      } else {
        otherLines.push(line);
      }
    }
    
    // 按照首选编解码器排序
    for (const codec of preferredCodecs) {
      for (const [pt, codecName] of codecMap.entries()) {
        if (codecName === codec.toUpperCase()) {
          payloadTypes.push(pt);
        }
      }
    }
    
    // 重建媒体行
    let mediaLine = '';
    for (const line of otherLines) {
      if (line.startsWith('m=')) {
        const parts = line.split(' ');
        mediaLine = `${parts[0]} ${parts[1]} ${parts[2]}`;
        for (const pt of payloadTypes) {
          mediaLine += ` ${pt}`;
        }
      }
    }
    
    // 重建媒体部分
    let result = mediaLine + '\r\n';
    for (const line of otherLines) {
      if (!line.startsWith('m=')) {
        result += line + '\r\n';
      }
    }
    
    // 添加编解码器相关的行
    for (const pt of payloadTypes) {
      // 添加rtpmap行
      for (const line of rtpmapLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
      
      // 添加fmtp行
      for (const line of fmtpLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
      
      // 添加rtcp-fb行
      for (const line of rtcpFbLines) {
        if (line.includes(`:${pt} `)) {
          result += line + '\r\n';
        }
      }
    }
    
    return result;
  }
}
