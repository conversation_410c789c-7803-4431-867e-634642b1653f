import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { WhipHandler } from '../protocols/whip';
import { WhepHandler } from '../protocols/whep';
import { StreamManager } from '../stream/manager';
import { AuthManager } from '../auth/auth-manager';
import { authMiddleware, permissionMiddleware, whipWhepAuthMiddleware } from '../auth/auth-middleware';
import { MetricsManager } from '../metrics/metrics-manager';
import { httpMetricsMiddleware } from '../metrics/http-metrics';
import { CascadeManager } from '../cascade/cascade-manager';
import { setupApiRoutes } from '../api/routes';
import { ServerConfig } from '../types';
import { getLogger, initLogger } from '../utils/logger';

const logger = getLogger();

export class EdgeServer {
  private app = express();
  private streamManager: StreamManager;
  private authManager: AuthManager;
  private metricsManager: MetricsManager;
  private cascadeManager: CascadeManager;
  private whipHandler: WhipHandler;
  private whepHandler: WhepHandler;

  constructor(private config: ServerConfig) {
    // 初始化日志系统
    initLogger(config);

    // 初始化各个管理器
    this.initializeManagers();

    // 设置中间件和路由
    this.setupMiddleware();
    this.setupRoutes();
  }

  private initializeManagers(): void {
    // 初始化指标管理器
    this.metricsManager = new MetricsManager();

    // 初始化认证管理器
    this.authManager = new AuthManager({
      enabled: this.config.auth?.enabled || false,
      secret: this.config.auth?.secret || 'default-secret',
      tokenExpiry: this.config.auth?.tokenExpiry || 3600,
      bcryptRounds: 10
    });

    // 添加静态token（如果配置了）
    if (this.config.auth?.enabled && process.env.STATIC_TOKENS) {
      const tokens = process.env.STATIC_TOKENS.split(',');
      tokens.forEach(token => this.authManager.addStaticToken(token.trim()));
    }

    // 初始化流管理器
    this.streamManager = new StreamManager(this.config.webrtc?.iceServers);

    // 初始化级联管理器
    this.cascadeManager = new CascadeManager({
      mode: this.config.cascade?.mode || 'auto',
      nodes: this.config.cascade?.nodes || [],
      healthCheckInterval: 30000,
      maxRetries: 3,
      timeout: 5000
    }, this.streamManager);

    // 初始化协议处理器
    this.whipHandler = new WhipHandler(this.streamManager);
    this.whepHandler = new WhepHandler(this.streamManager);

    logger.info('所有管理器初始化完成');
  }

  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet());

    // CORS中间件
    if (this.config.http.cors) {
      this.app.use(cors());
    }

    // HTTP指标中间件
    this.app.use(httpMetricsMiddleware(this.metricsManager));

    // 请求解析中间件
    this.app.use(express.text({ type: 'application/sdp' }));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  private setupRoutes(): void {
    // WHIP/WHEP认证中间件
    const whipWhepAuth = this.config.auth?.enabled
      ? whipWhepAuthMiddleware(this.authManager)
      : (req: express.Request, res: express.Response, next: express.NextFunction) => next();

    // WHIP路由
    this.app.post('/whip/:streamId', whipWhepAuth, this.whipHandler.handleRequest.bind(this.whipHandler));
    this.app.delete('/whip/:streamId/:sessionId', whipWhepAuth, this.whipHandler.handleSessionDelete.bind(this.whipHandler));

    // WHEP路由
    this.app.post('/whep/:streamId', whipWhepAuth, this.whepHandler.handleRequest.bind(this.whepHandler));
    this.app.delete('/whep/:streamId/:sessionId', whipWhepAuth, this.whepHandler.handleSessionDelete.bind(this.whepHandler));

    // API认证中间件
    const apiAuth = this.config.auth?.enabled
      ? authMiddleware(this.authManager)
      : (req: express.Request, res: express.Response, next: express.NextFunction) => next();

    // 指标路由
    this.app.get('/metrics', async (req, res) => {
      const metrics = await this.metricsManager.getMetrics();
      res.set('Content-Type', 'text/plain');
      res.send(metrics);
    });

    // API路由
    const apiRouter = setupApiRoutes(this.streamManager, this.cascadeManager, this.authManager);
    this.app.use('/api', apiAuth, apiRouter);

    // 健康检查路由
    this.app.get('/health', (req, res) => {
      res.json({ status: 'ok', version: process.env.npm_package_version || '1.0.0' });
    });

    logger.info('所有路由设置完成');
  }

  async start(): Promise<void> {
    try {
      // 解析监听地址
      const [host, portStr] = this.config.http.listen.split(':');
      const port = parseInt(portStr) || 8080;

      // 启动HTTP服务器
      this.app.listen(port, host, () => {
        logger.info(`Edge服务器已启动，监听地址: ${this.config.http.listen}`);
        logger.info(`公共访问地址: ${this.config.http.public}`);
      });

      // 注册进程退出处理
      this.registerShutdownHandlers();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`服务器启动失败: ${errorMessage}`);
      throw error;
    }
  }

  private registerShutdownHandlers(): void {
    const shutdown = async () => {
      logger.info('正在关闭服务器...');

      // 停止级联管理器
      this.cascadeManager.stop();

      // 等待1秒确保所有连接都能正常关闭
      await new Promise(resolve => setTimeout(resolve, 1000));

      logger.info('服务器已关闭');
      process.exit(0);
    };

    // 监听进程信号
    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    process.on('SIGHUP', shutdown);
  }
}