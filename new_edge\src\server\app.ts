import express from 'express';
import { WhipHand<PERSON> } from '../protocols/whip';
import { WhepHandler } from '../protocols/whep';
import { StreamManager } from '../stream/manager';

export class EdgeServer {
  private app = express();
  private whipHandler = new WhipHandler();
  private whepHandler = new WhepHandler();
  private streamManager = new StreamManager();

  constructor(private config: ServerConfig) {
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.text({ type: 'application/sdp' }));
    this.app.use(express.json());
  }

  private setupRoutes(): void {
    // WHIP routes
    this.app.post('/whip/:streamId', this.whipHandler.handleOffer.bind(this.whipHandler));
    
    // WHEP routes  
    this.app.post('/whep/:streamId', this.whepHandler.handleOffer.bind(this.whepHandler));
    
    // Management API
    this.app.get('/streams', this.getStreams.bind(this));
    this.app.delete('/session/:streamId/:clientId', this.deleteSession.bind(this));
  }

  async start(): Promise<void> {
    const port = this.config.http.listen.split(':')[1] || 8080;
    this.app.listen(port, () => {
      console.log(`Edge server listening on port ${port}`);
    });
  }
}