import { getLogger } from '../utils/logger';
import { StreamManager } from '../stream/manager';

const logger = getLogger();

/**
 * 节点信息
 */
export interface Node {
  id: string;
  alias: string;
  url: string;
  token?: string;
  active: boolean;
  lastSeen: number;
  metrics?: NodeMetrics;
}

/**
 * 节点指标
 */
export interface NodeMetrics {
  cpu: number;
  memory: number;
  network: {
    rx: number;
    tx: number;
  };
  streams: number;
  clients: number;
  uptime: number;
}

/**
 * 级联配置
 */
export interface CascadeConfig {
  mode: 'auto' | 'manual';
  nodes: string[];
  healthCheckInterval: number;
  maxRetries: number;
  timeout: number;
}

/**
 * 级联请求
 */
export interface CascadeRequest {
  sourceUrl?: string;
  targetUrl?: string;
  token?: string;
}

/**
 * 级联管理器
 */
export class CascadeManager {
  private nodes = new Map<string, Node>();
  private config: CascadeConfig;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(
    config: CascadeConfig,
    private streamManager: StreamManager
  ) {
    this.config = config;
    
    // 启动健康检查
    this.startHealthCheck();
    
    logger.info('级联管理器初始化完成');
  }

  /**
   * 添加节点
   * @param node 节点信息
   */
  addNode(node: Omit<Node, 'active' | 'lastSeen'>): void {
    const fullNode: Node = {
      ...node,
      active: false,
      lastSeen: 0
    };
    
    this.nodes.set(node.id, fullNode);
    logger.info(`添加节点: ${node.alias} (${node.url})`);
    
    // 立即检查节点健康状态
    this.checkNodeHealth(node.id);
  }

  /**
   * 移除节点
   * @param nodeId 节点ID
   * @returns 是否成功移除
   */
  removeNode(nodeId: string): boolean {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return false;
    }
    
    this.nodes.delete(nodeId);
    logger.info(`移除节点: ${node.alias}`);
    return true;
  }

  /**
   * 获取所有节点
   * @returns 节点列表
   */
  getAllNodes(): Node[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 获取活跃节点
   * @returns 活跃节点列表
   */
  getActiveNodes(): Node[] {
    return Array.from(this.nodes.values()).filter(node => node.active);
  }

  /**
   * 选择最佳节点
   * @param excludeNodes 排除的节点ID列表
   * @returns 最佳节点
   */
  selectBestNode(excludeNodes: string[] = []): Node | null {
    const availableNodes = this.getActiveNodes()
      .filter(node => !excludeNodes.includes(node.id));
    
    if (availableNodes.length === 0) {
      return null;
    }
    
    // 根据负载选择最佳节点
    return availableNodes.reduce((best, current) => {
      const bestLoad = this.calculateNodeLoad(best);
      const currentLoad = this.calculateNodeLoad(current);
      return currentLoad < bestLoad ? current : best;
    });
  }

  /**
   * 处理级联推流请求
   * @param streamId 流ID
   * @param request 级联请求
   */
  async handleCascadePush(streamId: string, request: CascadeRequest): Promise<void> {
    if (!request.targetUrl) {
      throw new Error('目标URL不能为空');
    }

    // 检查流是否存在
    const stream = this.streamManager.getStream(streamId);
    if (!stream || !stream.publish.active) {
      throw new Error(`流 ${streamId} 不存在或没有活跃的发布者`);
    }

    try {
      // 创建级联推流连接
      await this.createCascadePush(streamId, request.targetUrl, request.token);
      logger.info(`级联推流成功: ${streamId} -> ${request.targetUrl}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`级联推流失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 处理级联拉流请求
   * @param streamId 流ID
   * @param request 级联请求
   */
  async handleCascadePull(streamId: string, request: CascadeRequest): Promise<void> {
    if (!request.sourceUrl) {
      throw new Error('源URL不能为空');
    }

    try {
      // 创建级联拉流连接
      await this.createCascadePull(streamId, request.sourceUrl, request.token);
      logger.info(`级联拉流成功: ${streamId} <- ${request.sourceUrl}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`级联拉流失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 自动级联新节点
   * @param streamId 流ID
   * @param currentNodes 当前节点列表
   * @returns 新节点
   */
  async autoSelectNewNode(streamId: string, currentNodes: Node[]): Promise<Node | null> {
    if (this.config.mode !== 'auto') {
      return null;
    }

    const currentNodeIds = currentNodes.map(node => node.id);
    const newNode = this.selectBestNode(currentNodeIds);
    
    if (!newNode) {
      logger.warn(`没有可用的新节点用于流 ${streamId}`);
      return null;
    }

    try {
      // 自动创建级联连接
      const sourceNode = currentNodes[0]; // 使用第一个节点作为源
      await this.createAutoCascade(streamId, sourceNode, newNode);
      logger.info(`自动级联成功: ${streamId} ${sourceNode.alias} -> ${newNode.alias}`);
      return newNode;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`自动级联失败: ${errorMessage}`);
      return null;
    }
  }

  /**
   * 创建级联推流连接
   */
  private async createCascadePush(streamId: string, targetUrl: string, token?: string): Promise<void> {
    // 在实际实现中，这里应该创建WebRTC连接到目标节点
    // 由于浏览器WebRTC API的限制，这里只是模拟
    logger.debug(`创建级联推流连接: ${streamId} -> ${targetUrl}`);
    
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * 创建级联拉流连接
   */
  private async createCascadePull(streamId: string, sourceUrl: string, token?: string): Promise<void> {
    // 在实际实现中，这里应该从源节点拉取流
    // 由于浏览器WebRTC API的限制，这里只是模拟
    logger.debug(`创建级联拉流连接: ${streamId} <- ${sourceUrl}`);
    
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * 创建自动级联连接
   */
  private async createAutoCascade(streamId: string, sourceNode: Node, targetNode: Node): Promise<void> {
    const targetUrl = `${targetNode.url}/whip/${streamId}`;
    await this.createCascadePush(streamId, targetUrl, targetNode.token);
  }

  /**
   * 计算节点负载
   */
  private calculateNodeLoad(node: Node): number {
    if (!node.metrics) {
      return 0;
    }
    
    // 简单的负载计算：CPU + 内存使用率 + 流数量权重
    return node.metrics.cpu + node.metrics.memory + (node.metrics.streams * 10);
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const promises = Array.from(this.nodes.keys()).map(nodeId => 
      this.checkNodeHealth(nodeId)
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * 检查单个节点健康状态
   */
  private async checkNodeHealth(nodeId: string): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }

    try {
      // 在实际实现中，这里应该发送HTTP请求检查节点状态
      // 这里只是模拟
      const isHealthy = await this.pingNode(node);
      
      node.active = isHealthy;
      node.lastSeen = Date.now();
      
      if (isHealthy) {
        // 获取节点指标
        node.metrics = await this.getNodeMetrics(node);
      }
      
    } catch (error) {
      node.active = false;
      logger.warn(`节点健康检查失败: ${node.alias} - ${error}`);
    }
  }

  /**
   * Ping节点
   */
  private async pingNode(node: Node): Promise<boolean> {
    // 模拟网络请求
    return new Promise(resolve => {
      setTimeout(() => {
        // 90%的概率返回健康状态
        resolve(Math.random() > 0.1);
      }, 100);
    });
  }

  /**
   * 获取节点指标
   */
  private async getNodeMetrics(node: Node): Promise<NodeMetrics> {
    // 模拟指标数据
    return {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      network: {
        rx: Math.random() * 1000000,
        tx: Math.random() * 1000000
      },
      streams: Math.floor(Math.random() * 10),
      clients: Math.floor(Math.random() * 100),
      uptime: Date.now() - node.lastSeen
    };
  }

  /**
   * 停止健康检查
   */
  stop(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
    logger.info('级联管理器已停止');
  }
}
