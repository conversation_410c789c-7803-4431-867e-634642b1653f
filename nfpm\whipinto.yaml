name: "whipinto"
arch: "${NFPM_ARCH}"
platform: "linux"
version: "${NFPM_VERSION}"
release: "${NFPM_RELEASE}"
prerelease: "${NFPM_PRERELEASE}"
section: "utility"
priority: "optional"
maintainer: "BinBat Ltd <<EMAIL>>"
description: |
  A very simple, high performance, edge WebRTC SFU.
  RTP/RTSP to WHIP tool
vendor: "BinBat"
homepage: "http://live777.binbat.com"
license: "MPL-2.0"
contents:
  - src: ./target/${NFPM_TARGET}/release/whipinto
    dst: /usr/bin/whipinto

