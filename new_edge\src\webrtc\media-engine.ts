import { getLogger } from '../utils/logger';

const logger = getLogger();

// 简单的事件发射器实现
class SimpleEventEmitter {
  private listeners = new Map<string, Function[]>();

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }
}

/**
 * 支持的视频编解码器
 */
export enum VideoCodec {
  VP8 = 'VP8',
  VP9 = 'VP9',
  H264 = 'H264',
  AV1 = 'AV1'
}

/**
 * 支持的音频编解码器
 */
export enum AudioCodec {
  OPUS = 'opus',
  G722 = 'G722',
  PCMU = 'PCMU',
  PCMA = 'PCMA'
}

/**
 * 媒体轨道信息
 */
export interface MediaTrackInfo {
  id: string;
  kind: 'audio' | 'video';
  codec: VideoCodec | AudioCodec;
  rid?: string; // 用于SVC
  active: boolean;
  muted: boolean;
  parameters?: RTCRtpEncodingParameters;
}

/**
 * 媒体信息
 */
export interface MediaInfo {
  videoTracks: MediaTrackInfo[];
  audioTracks: MediaTrackInfo[];
  hasDataChannel: boolean;
  isSVC: boolean;
}

/**
 * 媒体引擎 - 负责处理媒体流、编解码器协商和RTP包转发
 */
export class MediaEngine extends SimpleEventEmitter {
  private streams = new Map<string, MediaStream>();
  private trackInfos = new Map<string, Map<string, MediaTrackInfo>>();
  private svcStreams = new Set<string>();

  constructor() {
    super();
  }

  /**
   * 从SDP中提取媒体信息
   * @param sdp SDP字符串
   * @returns 媒体信息
   */
  public extractMediaInfo(sdp: string): MediaInfo {
    const mediaInfo: MediaInfo = {
      videoTracks: [],
      audioTracks: [],
      hasDataChannel: false,
      isSVC: false
    };

    // 检查是否包含数据通道
    if (sdp.includes('application') && sdp.includes('webrtc-datachannel')) {
      mediaInfo.hasDataChannel = true;
    }

    // 解析视频轨道
    const videoSections = this.extractMediaSections(sdp, 'video');
    for (const section of videoSections) {
      const trackId = this.extractMid(section) || `video-${Date.now()}`;
      const codec = this.extractCodec(section, 'video');
      const rid = this.extractRid(section);
      
      // 检查是否是SVC
      if (rid) {
        mediaInfo.isSVC = true;
      }

      mediaInfo.videoTracks.push({
        id: trackId,
        kind: 'video',
        codec: codec as VideoCodec,
        rid,
        active: true,
        muted: false
      });
    }

    // 解析音频轨道
    const audioSections = this.extractMediaSections(sdp, 'audio');
    for (const section of audioSections) {
      const trackId = this.extractMid(section) || `audio-${Date.now()}`;
      const codec = this.extractCodec(section, 'audio');

      mediaInfo.audioTracks.push({
        id: trackId,
        kind: 'audio',
        codec: codec as AudioCodec,
        active: true,
        muted: false
      });
    }

    return mediaInfo;
  }

  /**
   * 从SDP中提取媒体部分
   */
  private extractMediaSections(sdp: string, mediaType: string): string[] {
    const sections: string[] = [];
    const lines = sdp.split('\r\n');
    let currentSection = '';
    let inMediaSection = false;
    let isTargetMediaType = false;

    for (const line of lines) {
      if (line.startsWith('m=')) {
        // 如果已经在一个媒体部分中，保存之前的部分
        if (inMediaSection && isTargetMediaType) {
          sections.push(currentSection);
        }
        
        // 开始新的媒体部分
        currentSection = line + '\r\n';
        inMediaSection = true;
        isTargetMediaType = line.startsWith(`m=${mediaType}`);
      } else if (inMediaSection) {
        currentSection += line + '\r\n';
      }
    }

    // 添加最后一个部分
    if (inMediaSection && isTargetMediaType) {
      sections.push(currentSection);
    }

    return sections;
  }

  /**
   * 从媒体部分提取MID
   */
  private extractMid(mediaSection: string): string | null {
    const midMatch = mediaSection.match(/a=mid:(\S+)/);
    return midMatch ? midMatch[1] : null;
  }

  /**
   * 从媒体部分提取编解码器
   */
  private extractCodec(mediaSection: string, mediaType: string): string {
    // 默认编解码器
    const defaultCodec = mediaType === 'video' ? VideoCodec.VP8 : AudioCodec.OPUS;
    
    // 尝试从SDP中提取编解码器
    if (mediaType === 'video') {
      if (mediaSection.includes(' VP8/')) return VideoCodec.VP8;
      if (mediaSection.includes(' VP9/')) return VideoCodec.VP9;
      if (mediaSection.includes(' H264/')) return VideoCodec.H264;
      if (mediaSection.includes(' AV1/')) return VideoCodec.AV1;
    } else {
      if (mediaSection.includes(' opus/')) return AudioCodec.OPUS;
      if (mediaSection.includes(' G722/')) return AudioCodec.G722;
      if (mediaSection.includes(' PCMU/')) return AudioCodec.PCMU;
      if (mediaSection.includes(' PCMA/')) return AudioCodec.PCMA;
    }

    return defaultCodec;
  }

  /**
   * 从媒体部分提取RID (用于SVC)
   */
  private extractRid(mediaSection: string): string | undefined {
    const ridMatch = mediaSection.match(/a=rid:(\S+) send/);
    return ridMatch ? ridMatch[1] : undefined;
  }
}
