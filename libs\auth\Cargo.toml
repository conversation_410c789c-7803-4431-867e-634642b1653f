[package]
name = "auth"
edition.workspace = true

[lib]
crate-type = ["lib"]

[dependencies]
api = { path = "../api" }
anyhow = { workspace = true, features = ["backtrace"] }
http = { workspace = true }
http-body = { workspace = true }
axum = { version = "0.7" }
jsonwebtoken = "9.3"
serde = { workspace = true, features = ["serde_derive"] }

headers = "0.4.0"
tower-http = { version = "0.5.2", features = ["validate-request"] }
