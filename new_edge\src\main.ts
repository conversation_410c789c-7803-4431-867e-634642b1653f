import { EdgeServer } from './server/app';
import { ServerConfig } from './types';
import { getLogger } from './utils/logger';

async function main() {
  // 默认配置
  const config: ServerConfig = {
    http: {
      listen: process.env.HTTP_LISTEN || '0.0.0.0:8080',
      public: process.env.HTTP_PUBLIC || 'http://localhost:8080',
      cors: process.env.HTTP_CORS === 'true'
    },
    webrtc: {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ],
      maxBitrate: parseInt(process.env.WEBRTC_MAX_BITRATE || '0') || undefined
    },
    log: {
      level: (process.env.LOG_LEVEL as any) || 'INFO',
      file: process.env.LOG_FILE
    },
    cascade: {
      mode: (process.env.CASCADE_MODE as 'auto' | 'manual') || 'auto',
      nodes: process.env.CASCADE_NODES ? process.env.CASCADE_NODES.split(',') : []
    },
    auth: {
      enabled: process.env.AUTH_ENABLED === 'true',
      secret: process.env.AUTH_SECRET || 'live777-secret-key',
      tokenExpiry: parseInt(process.env.AUTH_TOKEN_EXPIRY || '3600')
    }
  };

  try {
    // 创建并启动服务器
    const server = new EdgeServer(config);
    await server.start();

    const logger = getLogger();
    logger.info('Live777 Edge服务器启动成功');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('服务器启动失败:', errorMessage);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

main().catch((error) => {
  console.error('启动过程中发生错误:', error);
  process.exit(1);
});