import { EdgeServer } from './server/app';
import { ServerConfig } from './types/config';

async function main() {
  const config: ServerConfig = {
    http: {
      listen: '0.0.0.0:8080',
      public: 'http://localhost:8080'
    },
    log: {
      level: 'INFO'
    },
    cascade: {
      mode: 'auto'
    }
  };

  const server = new EdgeServer(config);
  await server.start();

  process.on('SIGINT', () => {
    console.log('Server shutting down...');
    process.exit(0);
  });
}

main().catch(console.error);