import { getLogger } from '../utils/logger';
import { SessionInfo, ClientInfo } from '../types';
import { MediaEngine, MediaInfo } from './media-engine';
import { RtpForwarder } from './rtp-forwarder';
import { SdpUtils } from './sdp-utils';

const logger = getLogger();

// 简单的事件发射器实现
class SimpleEventEmitter {
  private listeners = new Map<string, Function[]>();

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }
}

export class PeerConnectionManager extends SimpleEventEmitter {
  private config: RTCConfiguration;
  private sessions = new Map<string, SessionInfo>();
  private streams = new Map<string, MediaStream>();
  private publisherTracks = new Map<string, Map<string, MediaStreamTrack>>();
  private mediaEngine: MediaEngine;
  private rtpForwarder: RtpForwarder;

  constructor(iceServers: RTCIceServer[] = [{ urls: 'stun:stun.l.google.com:19302' }]) {
    super();
    this.config = {
      iceServers,
      iceCandidatePoolSize: 10,
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require'
    };

    this.mediaEngine = new MediaEngine();
    this.rtpForwarder = new RtpForwarder();

    // 设置RTP转发器事件监听
    this.setupRtpForwarderEvents();
  }

  /**
   * 设置RTP转发器事件监听
   */
  private setupRtpForwarderEvents(): void {
    // 处理关键帧请求
    this.rtpForwarder.on('request-keyframe', (streamId: string, trackId: string, ssrc: number) => {
      const session = this.getPublisherSession(streamId);
      if (session && session.peerConnection) {
        // 发送PLI (Picture Loss Indication)
        const sender = this.findSenderByTrackId(session.peerConnection, trackId);
        if (sender) {
          try {
            session.peerConnection.getStats(sender).then(stats => {
              logger.debug(`发送关键帧请求: ${streamId}/${trackId}`);
            });
          } catch (error) {
            logger.error(`发送关键帧请求失败: ${error}`);
          }
        }
      }
    });

    // 处理RTP包转发
    this.rtpForwarder.on('forward-rtp', (streamId: string, trackId: string, packet: any) => {
      // 在实际实现中，这里应该将RTP包转发给订阅者
      // 由于浏览器WebRTC API限制，我们无法直接操作RTP包
      // 这里只是记录日志
      logger.debug(`转发RTP包: ${streamId}/${trackId}`);
    });

    // 处理RTCP包转发
    this.rtpForwarder.on('forward-rtcp', (streamId: string, packet: any) => {
      // 同上，由于API限制，这里只记录日志
      logger.debug(`转发RTCP包: ${streamId}`);
    });
  }

  /**
   * 获取发布者会话
   */
  private getPublisherSession(streamId: string): SessionInfo | undefined {
    for (const session of this.sessions.values()) {
      if (session.streamId === streamId && session.clientInfo.type === 'publisher') {
        return session;
      }
    }
    return undefined;
  }

  /**
   * 根据轨道ID查找发送器
   */
  private findSenderByTrackId(peerConnection: RTCPeerConnection, trackId: string): RTCRtpSender | undefined {
    const senders = peerConnection.getSenders();
    for (const sender of senders) {
      if (sender.track && sender.track.id === trackId) {
        return sender;
      }
    }
    return undefined;
  }

  /**
   * 创建发布者连接
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   */
  async createPublisher(streamId: string, clientInfo: any, offer: string): Promise<{ sessionId: string, answer: string }> {
    const sessionId = `pub_${streamId}_${Date.now()}`;
    
    try {
      const peerConnection = new RTCPeerConnection(this.config);
      
      // 设置事件监听
      this.setupPeerEvents(peerConnection, sessionId, streamId);
      
      // 处理媒体轨道
      peerConnection.ontrack = (event) => {
        logger.info(`收到发布者轨道: ${event.track.kind} for stream ${streamId}`);
        this.emit('track', streamId, event.track, event.streams[0]);
      };

      // 设置远程描述（offer）
      await peerConnection.setRemoteDescription({ type: 'offer', sdp: offer });
      
      // 创建应答
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // 保存会话信息
      const sessionInfo: SessionInfo = {
        id: sessionId,
        streamId,
        clientInfo: {
          ...clientInfo,
          type: 'publisher'
        },
        peerConnection
      };
      
      this.sessions.set(sessionId, sessionInfo);
      
      logger.info(`创建发布者连接成功: ${sessionId} for stream ${streamId}`);
      this.emit('publisher:created', streamId, sessionId);
      
      return {
        sessionId,
        answer: answer.sdp || ''
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建发布者连接失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 创建订阅者连接
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   */
  async createSubscriber(streamId: string, clientInfo: any, offer: string): Promise<{ sessionId: string, answer: string }> {
    const sessionId = `sub_${streamId}_${Date.now()}`;
    
    try {
      const peerConnection = new RTCPeerConnection(this.config);
      
      // 设置事件监听
      this.setupPeerEvents(peerConnection, sessionId, streamId);
      
      // 添加媒体轨道
      const stream = this.streams.get(streamId);
      if (stream) {
        stream.getTracks().forEach(track => {
          peerConnection.addTrack(track, stream);
        });
      } else {
        throw new Error(`找不到流: ${streamId}`);
      }

      // 设置远程描述（offer）
      await peerConnection.setRemoteDescription({ type: 'offer', sdp: offer });
      
      // 创建应答
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // 保存会话信息
      const sessionInfo: SessionInfo = {
        id: sessionId,
        streamId,
        clientInfo: {
          ...clientInfo,
          type: 'subscriber'
        },
        peerConnection
      };
      
      this.sessions.set(sessionId, sessionInfo);
      
      logger.info(`创建订阅者连接成功: ${sessionId} for stream ${streamId}`);
      this.emit('subscriber:created', streamId, sessionId);
      
      return {
        sessionId,
        answer: answer.sdp || ''
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`创建订阅者连接失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 关闭会话
   * @param sessionId 会话ID
   */
  closeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    try {
      if (session.peerConnection) {
        session.peerConnection.close();
      }
      
      this.sessions.delete(sessionId);
      
      const eventType = sessionId.startsWith('pub_') ? 'publisher:closed' : 'subscriber:closed';
      this.emit(eventType, session.streamId, sessionId);
      
      logger.info(`关闭会话: ${sessionId} for stream ${session.streamId}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`关闭会话失败: ${errorMessage}`);
      return false;
    }
  }

  /**
   * 设置对等连接事件
   */
  private setupPeerEvents(peerConnection: RTCPeerConnection, sessionId: string, streamId: string): void {
    peerConnection.oniceconnectionstatechange = () => {
      logger.debug(`ICE 连接状态变更: ${peerConnection.iceConnectionState} for session ${sessionId}`);
      
      if (peerConnection.iceConnectionState === 'disconnected' || 
          peerConnection.iceConnectionState === 'failed' ||
          peerConnection.iceConnectionState === 'closed') {
        this.emit('connection:closed', streamId, sessionId);
      }
    };

    peerConnection.onconnectionstatechange = () => {
      logger.debug(`连接状态变更: ${peerConnection.connectionState} for session ${sessionId}`);
    };

    peerConnection.onicecandidateerror = (event) => {
      logger.warn(`ICE 候选错误: ${event.errorText} for session ${sessionId}`);
    };
  }

  /**
   * 获取会话信息
   */
  getSession(sessionId: string): SessionInfo | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 获取流的所有会话
   */
  getStreamSessions(streamId: string): SessionInfo[] {
    return Array.from(this.sessions.values())
      .filter(session => session.streamId === streamId);
  }
}