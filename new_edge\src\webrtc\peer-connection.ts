import { EventEmitter } from 'events';
import { getLogger } from '../utils/logger';
import { SessionInfo } from '../types';

const logger = getLogger();

export class PeerConnectionManager extends EventEmitter {
  private config: RTCConfiguration;
  private sessions = new Map<string, SessionInfo>();

  constructor(iceServers: RTCIceServer[]) {
    super();
    this.config = {
      iceServers,
      iceCandidatePoolSize: 10
    };
  }

  /**
   * 创建发布者连接
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   */
  async createPublisher(streamId: string, clientInfo: any, offer: string): Promise<{ sessionId: string, answer: string }> {
    const sessionId = `pub_${streamId}_${Date.now()}`;
    
    try {
      const peerConnection = new RTCPeerConnection(this.config);
      
      // 设置事件监听
      this.setupPeerEvents(peerConnection, sessionId, streamId);
      
      // 处理媒体轨道
      peerConnection.ontrack = (event) => {
        logger.info(`收到发布者轨道: ${event.track.kind} for stream ${streamId}`);
        this.emit('track', streamId, event.track, event.streams[0]);
      };

      // 设置远程描述（offer）
      await peerConnection.setRemoteDescription({ type: 'offer', sdp: offer });
      
      // 创建应答
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // 保存会话信息
      const sessionInfo: SessionInfo = {
        id: sessionId,
        streamId,
        clientInfo: {
          ...clientInfo,
          type: 'publisher'
        },
        peerConnection
      };
      
      this.sessions.set(sessionId, sessionInfo);
      
      logger.info(`创建发布者连接成功: ${sessionId} for stream ${streamId}`);
      this.emit('publisher:created', streamId, sessionId);
      
      return {
        sessionId,
        answer: answer.sdp || ''
      };
    } catch (error) {
      logger.error(`创建发布者连接失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建订阅者连接
   * @param streamId 流ID
   * @param clientInfo 客户端信息
   * @param offer SDP offer
   */
  async createSubscriber(streamId: string, clientInfo: any, offer: string): Promise<{ sessionId: string, answer: string }> {
    const sessionId = `sub_${streamId}_${Date.now()}`;
    
    try {
      const peerConnection = new RTCPeerConnection(this.config);
      
      // 设置事件监听
      this.setupPeerEvents(peerConnection, sessionId, streamId);
      
      // 添加媒体轨道
      const stream = this.emit('getStream', streamId);
      if (stream) {
        stream.getTracks().forEach(track => {
          peerConnection.addTrack(track, stream);
        });
      } else {
        throw new Error(`找不到流: ${streamId}`);
      }

      // 设置远程描述（offer）
      await peerConnection.setRemoteDescription({ type: 'offer', sdp: offer });
      
      // 创建应答
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // 保存会话信息
      const sessionInfo: SessionInfo = {
        id: sessionId,
        streamId,
        clientInfo: {
          ...clientInfo,
          type: 'subscriber'
        },
        peerConnection
      };
      
      this.sessions.set(sessionId, sessionInfo);
      
      logger.info(`创建订阅者连接成功: ${sessionId} for stream ${streamId}`);
      this.emit('subscriber:created', streamId, sessionId);
      
      return {
        sessionId,
        answer: answer.sdp || ''
      };
    } catch (error) {
      logger.error(`创建订阅者连接失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 关闭会话
   * @param sessionId 会话ID
   */
  closeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    try {
      if (session.peerConnection) {
        session.peerConnection.close();
      }
      
      this.sessions.delete(sessionId);
      
      const eventType = sessionId.startsWith('pub_') ? 'publisher:closed' : 'subscriber:closed';
      this.emit(eventType, session.streamId, sessionId);
      
      logger.info(`关闭会话: ${sessionId} for stream ${session.streamId}`);
      return true;
    } catch (error) {
      logger.error(`关闭会话失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 设置对等连接事件
   */
  private setupPeerEvents(peerConnection: RTCPeerConnection, sessionId: string, streamId: string): void {
    peerConnection.oniceconnectionstatechange = () => {
      logger.debug(`ICE 连接状态变更: ${peerConnection.iceConnectionState} for session ${sessionId}`);
      
      if (peerConnection.iceConnectionState === 'disconnected' || 
          peerConnection.iceConnectionState === 'failed' ||
          peerConnection.iceConnectionState === 'closed') {
        this.emit('connection:closed', streamId, sessionId);
      }
    };

    peerConnection.onconnectionstatechange = () => {
      logger.debug(`连接状态变更: ${peerConnection.connectionState} for session ${sessionId}`);
    };

    peerConnection.onicecandidateerror = (event) => {
      logger.warn(`ICE 候选错误: ${event.errorText} for session ${sessionId}`);
    };
  }

  /**
   * 获取会话信息
   */
  getSession(sessionId: string): SessionInfo | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 获取流的所有会话
   */
  getStreamSessions(streamId: string): SessionInfo[] {
    return Array.from(this.sessions.values())
      .filter(session => session.streamId === streamId);
  }
}