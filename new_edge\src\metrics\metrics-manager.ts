import { register, Counter, Gauge, Histogram, collectDefaultMetrics } from 'prom-client';
import { getLogger } from '../utils/logger';

const logger = getLogger();

/**
 * 指标管理器
 */
export class MetricsManager {
  // 流相关指标
  public readonly streamCount: Gauge<string>;
  public readonly publishCount: Gauge<string>;
  public readonly subscribeCount: Gauge<string>;
  public readonly reforwardCount: Gauge<string>;
  
  // 会话相关指标
  public readonly sessionTotal: Counter<string>;
  public readonly sessionDuration: Histogram<string>;
  public readonly sessionErrors: Counter<string>;
  
  // WebRTC相关指标
  public readonly webrtcConnections: Gauge<string>;
  public readonly rtpPackets: Counter<string>;
  public readonly rtcpPackets: Counter<string>;
  public readonly keyFrameRequests: Counter<string>;
  
  // 系统指标
  public readonly httpRequests: Counter<string>;
  public readonly httpDuration: Histogram<string>;
  public readonly httpErrors: Counter<string>;

  constructor() {
    // 启用默认系统指标
    collectDefaultMetrics({ register });

    // 流相关指标
    this.streamCount = new Gauge({
      name: 'live777_streams_total',
      help: '当前活跃流数量',
      labelNames: ['status']
    });

    this.publishCount = new Gauge({
      name: 'live777_publishers_total',
      help: '当前发布者数量'
    });

    this.subscribeCount = new Gauge({
      name: 'live777_subscribers_total',
      help: '当前订阅者数量'
    });

    this.reforwardCount = new Gauge({
      name: 'live777_reforwards_total',
      help: '当前转发数量'
    });

    // 会话相关指标
    this.sessionTotal = new Counter({
      name: 'live777_sessions_total',
      help: '会话总数',
      labelNames: ['type', 'status']
    });

    this.sessionDuration = new Histogram({
      name: 'live777_session_duration_seconds',
      help: '会话持续时间',
      labelNames: ['type'],
      buckets: [1, 5, 10, 30, 60, 300, 600, 1800, 3600]
    });

    this.sessionErrors = new Counter({
      name: 'live777_session_errors_total',
      help: '会话错误总数',
      labelNames: ['type', 'error']
    });

    // WebRTC相关指标
    this.webrtcConnections = new Gauge({
      name: 'live777_webrtc_connections_total',
      help: 'WebRTC连接数量',
      labelNames: ['state']
    });

    this.rtpPackets = new Counter({
      name: 'live777_rtp_packets_total',
      help: 'RTP包总数',
      labelNames: ['direction', 'type']
    });

    this.rtcpPackets = new Counter({
      name: 'live777_rtcp_packets_total',
      help: 'RTCP包总数',
      labelNames: ['direction', 'type']
    });

    this.keyFrameRequests = new Counter({
      name: 'live777_keyframe_requests_total',
      help: '关键帧请求总数',
      labelNames: ['stream_id']
    });

    // HTTP相关指标
    this.httpRequests = new Counter({
      name: 'live777_http_requests_total',
      help: 'HTTP请求总数',
      labelNames: ['method', 'path', 'status']
    });

    this.httpDuration = new Histogram({
      name: 'live777_http_request_duration_seconds',
      help: 'HTTP请求持续时间',
      labelNames: ['method', 'path'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5]
    });

    this.httpErrors = new Counter({
      name: 'live777_http_errors_total',
      help: 'HTTP错误总数',
      labelNames: ['method', 'path', 'error']
    });

    // 注册所有指标
    this.registerMetrics();
    
    logger.info('指标管理器初始化完成');
  }

  /**
   * 注册所有指标
   */
  private registerMetrics(): void {
    register.registerMetric(this.streamCount);
    register.registerMetric(this.publishCount);
    register.registerMetric(this.subscribeCount);
    register.registerMetric(this.reforwardCount);
    register.registerMetric(this.sessionTotal);
    register.registerMetric(this.sessionDuration);
    register.registerMetric(this.sessionErrors);
    register.registerMetric(this.webrtcConnections);
    register.registerMetric(this.rtpPackets);
    register.registerMetric(this.rtcpPackets);
    register.registerMetric(this.keyFrameRequests);
    register.registerMetric(this.httpRequests);
    register.registerMetric(this.httpDuration);
    register.registerMetric(this.httpErrors);
  }

  /**
   * 记录流创建
   */
  recordStreamCreated(): void {
    this.streamCount.inc({ status: 'active' });
  }

  /**
   * 记录流删除
   */
  recordStreamDeleted(): void {
    this.streamCount.dec({ status: 'active' });
  }

  /**
   * 记录发布者连接
   */
  recordPublisherConnected(): void {
    this.publishCount.inc();
    this.sessionTotal.inc({ type: 'publisher', status: 'connected' });
  }

  /**
   * 记录发布者断开
   * @param duration 持续时间（秒）
   */
  recordPublisherDisconnected(duration: number): void {
    this.publishCount.dec();
    this.sessionTotal.inc({ type: 'publisher', status: 'disconnected' });
    this.sessionDuration.observe({ type: 'publisher' }, duration);
  }

  /**
   * 记录订阅者连接
   */
  recordSubscriberConnected(): void {
    this.subscribeCount.inc();
    this.sessionTotal.inc({ type: 'subscriber', status: 'connected' });
  }

  /**
   * 记录订阅者断开
   * @param duration 持续时间（秒）
   */
  recordSubscriberDisconnected(duration: number): void {
    this.subscribeCount.dec();
    this.sessionTotal.inc({ type: 'subscriber', status: 'disconnected' });
    this.sessionDuration.observe({ type: 'subscriber' }, duration);
  }

  /**
   * 记录会话错误
   * @param type 会话类型
   * @param error 错误类型
   */
  recordSessionError(type: string, error: string): void {
    this.sessionErrors.inc({ type, error });
  }

  /**
   * 记录WebRTC连接状态变化
   * @param state 连接状态
   * @param delta 变化量
   */
  recordWebRTCConnectionState(state: string, delta: number): void {
    if (delta > 0) {
      this.webrtcConnections.inc({ state }, delta);
    } else {
      this.webrtcConnections.dec({ state }, Math.abs(delta));
    }
  }

  /**
   * 记录RTP包
   * @param direction 方向
   * @param type 类型
   */
  recordRtpPacket(direction: string, type: string): void {
    this.rtpPackets.inc({ direction, type });
  }

  /**
   * 记录RTCP包
   * @param direction 方向
   * @param type 类型
   */
  recordRtcpPacket(direction: string, type: string): void {
    this.rtcpPackets.inc({ direction, type });
  }

  /**
   * 记录关键帧请求
   * @param streamId 流ID
   */
  recordKeyFrameRequest(streamId: string): void {
    this.keyFrameRequests.inc({ stream_id: streamId });
  }

  /**
   * 记录HTTP请求
   * @param method 请求方法
   * @param path 请求路径
   * @param status 状态码
   * @param duration 持续时间（秒）
   */
  recordHttpRequest(method: string, path: string, status: number, duration: number): void {
    this.httpRequests.inc({ method, path, status: status.toString() });
    this.httpDuration.observe({ method, path }, duration);
    
    if (status >= 400) {
      this.httpErrors.inc({ method, path, error: status.toString() });
    }
  }

  /**
   * 获取指标数据
   * @returns Prometheus格式的指标数据
   */
  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  /**
   * 重置所有指标
   */
  reset(): void {
    register.resetMetrics();
    logger.info('所有指标已重置');
  }
}
