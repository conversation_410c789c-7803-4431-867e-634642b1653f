# Live777 Edge Server (TypeScript版本)

这是Live777项目的TypeScript重构版本，提供了完整的WebRTC SFU功能，包括WHIP/WHEP协议支持、流管理、认证授权、级联转发和指标监控。

## 功能特性

### 核心功能
- ✅ **WHIP协议支持** - WebRTC-HTTP推流协议
- ✅ **WHEP协议支持** - WebRTC-HTTP拉流协议  
- ✅ **流管理** - 完整的流生命周期管理
- ✅ **WebRTC媒体处理** - 支持多种编解码器
- ✅ **会话管理** - 发布者和订阅者会话跟踪

### 高级功能
- ✅ **认证授权** - JWT token和权限管理
- ✅ **级联转发** - 多节点流转发
- ✅ **指标监控** - Prometheus指标导出
- ✅ **负载均衡** - 智能节点选择
- ✅ **健康检查** - 节点状态监控

### 编解码器支持
- **视频**: VP8, VP9, H264, AV1
- **音频**: Opus, G722, PCMU, PCMA
- **SVC**: 可伸缩视频编码支持

## 快速开始

### 安装依赖
```bash
npm install
```

### 配置
复制配置文件模板：
```bash
cp config.example.json config.json
```

编辑配置文件根据需要修改设置。

### 启动服务器
```bash
npm start
```

或者开发模式：
```bash
npm run dev
```

## API文档

### WHIP协议 (推流)
```bash
# 推流
POST /whip/{streamId}
Content-Type: application/sdp
Authorization: Bearer <token>

# 删除推流会话
DELETE /whip/{streamId}/{sessionId}
```

### WHEP协议 (拉流)
```bash
# 拉流
POST /whep/{streamId}
Content-Type: application/sdp
Authorization: Bearer <token>

# 删除拉流会话
DELETE /whep/{streamId}/{sessionId}
```

### 管理API

#### 流管理
```bash
# 获取所有流
GET /api/streams

# 获取特定流
GET /api/streams/{streamId}

# 创建流
POST /api/streams
{
  "streamId": "test-stream",
  "metadata": {}
}

# 删除流
DELETE /api/streams/{streamId}

# 获取流会话
GET /api/streams/{streamId}/sessions
```

#### 级联管理
```bash
# 获取所有节点
GET /api/cascade/nodes

# 添加节点
POST /api/cascade/nodes
{
  "id": "node1",
  "alias": "Node 1",
  "url": "http://node1.example.com:8080",
  "token": "optional-token"
}

# 删除节点
DELETE /api/cascade/nodes/{nodeId}

# 创建级联推流
POST /api/cascade/push/{streamId}
{
  "targetUrl": "http://target.example.com:8080/whip/stream1",
  "token": "optional-token"
}

# 创建级联拉流
POST /api/cascade/pull/{streamId}
{
  "sourceUrl": "http://source.example.com:8080/whep/stream1",
  "token": "optional-token"
}
```

#### 认证管理
```bash
# 用户登录
POST /api/auth/login
{
  "username": "admin",
  "password": "admin123"
}

# 获取所有用户
GET /api/auth/users

# 创建用户
POST /api/auth/users
{
  "username": "newuser",
  "password": "password",
  "permissions": [
    {
      "resource": "stream",
      "action": "read",
      "scope": "*"
    }
  ]
}

# 删除用户
DELETE /api/auth/users/{userId}
```

### 监控
```bash
# Prometheus指标
GET /metrics

# 健康检查
GET /health
```

## 配置说明

### 环境变量
- `HTTP_LISTEN` - HTTP监听地址 (默认: 0.0.0.0:8080)
- `HTTP_PUBLIC` - 公共访问地址 (默认: http://localhost:8080)
- `HTTP_CORS` - 启用CORS (默认: false)
- `WEBRTC_MAX_BITRATE` - 最大比特率
- `LOG_LEVEL` - 日志级别 (TRACE|DEBUG|INFO|WARN|ERROR)
- `LOG_FILE` - 日志文件路径
- `CASCADE_MODE` - 级联模式 (auto|manual)
- `CASCADE_NODES` - 级联节点列表 (逗号分隔)
- `AUTH_ENABLED` - 启用认证 (默认: false)
- `AUTH_SECRET` - JWT密钥
- `AUTH_TOKEN_EXPIRY` - Token过期时间 (秒)
- `STATIC_TOKENS` - 静态token列表 (逗号分隔)

### 配置文件
参考 `config.example.json` 文件进行配置。

## 开发

### 项目结构
```
src/
├── api/           # REST API路由
├── auth/          # 认证授权
├── cascade/       # 级联管理
├── metrics/       # 指标监控
├── protocols/     # WHIP/WHEP协议
├── server/        # HTTP服务器
├── stream/        # 流管理
├── types/         # 类型定义
├── utils/         # 工具函数
└── webrtc/        # WebRTC处理
```

### 构建
```bash
npm run build
```

### 测试
```bash
npm test
```

## 与原始Rust版本的对比

| 功能 | Rust版本 | TypeScript版本 | 状态 |
|------|----------|----------------|------|
| WHIP/WHEP协议 | ✅ | ✅ | 完成 |
| 流管理 | ✅ | ✅ | 完成 |
| WebRTC媒体处理 | ✅ | ✅ | 完成 |
| 认证授权 | ✅ | ✅ | 完成 |
| 级联转发 | ✅ | ✅ | 完成 |
| 指标监控 | ✅ | ✅ | 完成 |
| 集群管理 | ✅ | ✅ | 完成 |
| SVC支持 | ✅ | ✅ | 完成 |
| 多编解码器 | ✅ | ✅ | 完成 |
| 性能优化 | ✅ | 🔄 | 进行中 |

## 许可证

与原始Live777项目保持一致。
