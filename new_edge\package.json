{"name": "edge-webrtc-server", "version": "1.0.0", "description": "TypeScript WebRTC Edge Server", "main": "dist/main.js", "scripts": {"start": "node dist/main.js", "dev": "ts-node --watch src/main.ts", "build": "tsc", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured\"", "docker:build": "docker build -t live777-edge .", "docker:run": "docker run -p 8080:8080 live777-edge"}, "dependencies": {"express": "^4.18.0", "ws": "^8.13.0", "uuid": "^9.0.0", "winston": "^3.10.0", "cors": "^2.8.5", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "prom-client": "^14.2.0", "node-cron": "^3.0.2"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/ws": "^8.5.0", "@types/uuid": "^9.0.0", "@types/cors": "^2.8.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}}