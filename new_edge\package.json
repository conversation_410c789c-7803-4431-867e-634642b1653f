{"name": "edge-webrtc-server", "version": "1.0.0", "description": "TypeScript WebRTC Edge Server", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node src/main.ts"}, "dependencies": {"express": "^4.18.0", "ws": "^8.13.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/ws": "^8.5.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}}