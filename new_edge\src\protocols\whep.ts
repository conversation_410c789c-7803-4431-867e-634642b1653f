import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { getLogger } from '../utils/logger';
import { PeerConnectionManager } from '../webrtc/peer-connection';
import { StreamManager } from '../stream/manager';
import { ClientInfo } from '../types';

const logger = getLogger();

export class WhepHandler {
  constructor(
    private peerManager: PeerConnectionManager,
    private streamManager: StreamManager
  ) {}

  /**
   * 处理WHEP请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleRequest(req: Request, res: Response): Promise<void> {
    const { streamId } = req.params;
    
    if (!streamId) {
      res.status(400).send('Missing streamId');
      return;
    }

    // 检查Content-Type
    if (req.headers['content-type'] !== 'application/sdp') {
      res.status(415).send('Content-Type must be application/sdp');
      return;
    }

    const sdpOffer = req.body;
    if (!sdpOffer) {
      res.status(400).send('Missing SDP offer');
      return;
    }

    try {
      // 检查流是否存在且有发布者
      const stream = this.streamManager.getStream(streamId);
      if (!stream) {
        res.status(404).send('Stream not found');
        return;
      }

      if (!stream.publish.active) {
        res.status(409).send('Stream has no active publisher');
        return;
      }

      // 创建客户端信息
      const clientId = uuidv4();
      const clientInfo: ClientInfo = {
        id: clientId,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        startTime: Date.now(),
        type: 'subscriber'
      };

      // 创建订阅者连接
      const { sessionId, answer } = await this.peerManager.createSubscriber(
        streamId,
        clientInfo,
        sdpOffer
      );

      // 添加订阅者到流
      this.streamManager.addSubscriber(streamId, clientInfo);

      // 设置响应头
      res.setHeader('Content-Type', 'application/sdp');
      res.setHeader('Location', `/whep/${streamId}/${sessionId}`);
      res.setHeader('Link', '<https://www.ietf.org/archive/id/draft-ietf-wish-whep-00.html#name-ice-server-configuration>; rel="ice-server"');
      
      // 返回SDP应答
      res.status(201).send(answer);
      
      logger.info(`WHEP订阅成功: ${streamId} (${sessionId})`);
    } catch (error) {
      logger.error(`WHEP处理错误: ${error.message}`);
      res.status(500).send(`Error: ${error.message}`);
    }
  }

  /**
   * 处理WHEP DELETE请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleDelete(req: Request, res: Response): Promise<void> {
    const { streamId, sessionId } = req.params;
    
    if (!streamId || !sessionId) {
      res.status(400).send('Missing streamId or sessionId');
      return;
    }

    try {
      // 获取会话信息
      const session = this.peerManager.getSession(sessionId);
      if (!session) {
        res.status(404).send('Session not found');
        return;
      }

      // 关闭会话
      const success = this.peerManager.closeSession(sessionId);
      if (!success) {
        res.status(404).send('Failed to close session');
        return;
      }

      // 移除订阅者
      this.streamManager.removeSubscriber(streamId, session.clientInfo.id);

      res.status(200).send('OK');
      logger.info(`WHEP会话已关闭: ${streamId} (${sessionId})`);
    } catch (error) {
      logger.error(`WHEP删除错误: ${error.message}`);
      res.status(500).send(`Error: ${error.message}`);
    }
  }
}