import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { getLogger } from '../utils/logger';
import { StreamManager } from '../stream/manager';
import { ClientInfo } from '../types';

const logger = getLogger();

export class WhepHandler {
  constructor(private streamManager: StreamManager) {}

  /**
   * 处理WHEP请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleRequest(req: Request, res: Response): Promise<void> {
    const { streamId } = req.params;

    if (!streamId) {
      res.status(400).send('Missing streamId');
      return;
    }

    // 检查Content-Type
    if (req.headers['content-type'] !== 'application/sdp') {
      res.status(415).send('Content-Type must be application/sdp');
      return;
    }

    const sdpOffer = req.body;
    if (!sdpOffer) {
      res.status(400).send('Missing SDP offer');
      return;
    }

    try {
      // 创建客户端信息
      const clientId = uuidv4();
      const clientInfo: ClientInfo = {
        id: clientId,
        ip: req.ip || 'unknown',
        userAgent: req.headers['user-agent'],
        startTime: Date.now(),
        type: 'subscriber'
      };

      // 处理订阅请求
      const { sessionId, answer } = await this.streamManager.handleSubscribe(
        streamId,
        clientInfo,
        sdpOffer
      );

      // 设置响应头
      res.setHeader('Content-Type', 'application/sdp');
      res.setHeader('Location', `/whep/${streamId}/${sessionId}`);
      res.setHeader('Link', '<https://www.ietf.org/archive/id/draft-ietf-wish-whep-00.html#name-ice-server-configuration>; rel="ice-server"');

      // 返回SDP应答
      res.status(201).send(answer);

      logger.info(`WHEP订阅成功: ${streamId} (${sessionId})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`WHEP处理错误: ${errorMessage}`);
      res.status(500).send(`Error: ${errorMessage}`);
    }
  }

  /**
   * 处理WHEP DELETE请求
   * @param req 请求对象
   * @param res 响应对象
   */
  async handleDelete(req: Request, res: Response): Promise<void> {
    const { streamId, sessionId } = req.params;

    if (!streamId || !sessionId) {
      res.status(400).send('Missing streamId or sessionId');
      return;
    }

    try {
      // 获取会话信息
      const session = this.streamManager.getSession(sessionId);
      if (!session) {
        res.status(404).send('Session not found');
        return;
      }

      // 移除会话
      const success = this.streamManager.removeSession(sessionId);
      if (!success) {
        res.status(500).send('Failed to close session');
        return;
      }

      res.status(204).send();
      logger.info(`WHEP会话已关闭: ${streamId} (${sessionId})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`WHEP删除错误: ${errorMessage}`);
      res.status(500).send(`Error: ${errorMessage}`);
    }
  }
}