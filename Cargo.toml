[package]
name = "live777"
description = "A very simple, high performance, edge WebRTC SFU"
version.workspace = true
edition.workspace = true
license.workspace = true
authors.workspace = true
repository.workspace = true

default-run = "live777"

[[bin]]
name = "live777"
path = "src/main.rs"

[[bin]]
name = "liveman"
path = "src/liveman.rs"

[[bin]]
name = "livenil"
path = "src/livenil.rs"

[[bin]]
name = "whipinto"
path = "src/whipinto.rs"

[[bin]]
name = "whepfrom"
path = "src/whepfrom.rs"

[[bin]]
name = "net4mqtt"
path = "src/net4mqtt.rs"

[workspace]
members = [
    ".",
    "libs/api",
    "libs/cli",
    "libs/http-log",
    "libs/libwish",
    "libs/signal",
    "libs/net4mqtt",
    "liveion",
    "liveman",
    "livetwo",
]

[workspace.package]
version = "0.7.3"
edition = "2021"
license = "MPL-2.0"
authors = ["BinBat Ltd <<EMAIL>>"]
repository = "https://github.com/binbat/live777"

[workspace.dependencies]
webrtc = { git = "https://github.com/webrtc-rs/webrtc", rev = "ae93e81" }

anyhow = "1.0"
clap = "4.5"
http = "1.1"
http-body = "1.0"
serde = "1"
tokio = "1.36"
tracing = "0.1"

[dev-dependencies]
http = { workspace = true }
serde_json = "1.0"

api = { path = "libs/api" }
cli = { path = "libs/cli" }
reqwest = { version = "0.12", features = [
    "rustls-tls",
    "socks",
    "json"
], default-features = false }

tempfile = "3"

[dependencies]
liveion = { path = "liveion" }
liveman = { path = "liveman" }
livetwo = { path = "livetwo" }
net4mqtt = { path = "libs/net4mqtt" }
signal = { path = "libs/signal" }

clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
tracing = { workspace = true }
serde = { workspace = true }

toml = "0.8.19"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

[features]
webui = ["liveion/webui", "liveman/webui"]
net4mqtt = ["liveion/net4mqtt", "liveman/net4mqtt"]

